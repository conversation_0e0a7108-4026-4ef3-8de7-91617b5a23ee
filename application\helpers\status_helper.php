<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Status Helper
 * 
 * Centralized status management for all modules
 * Provides consistent status handling across the application
 */

if (!function_exists('get_status_config')) {
    /**
     * Get status configuration for all modules
     * 
     * @return array
     */
    function get_status_config() {
        return [
            // Warehouse Operations (Consistent)
            'barang_masuk' => [
                'draft' => ['label' => 'Draft', 'class' => 'badge-warning', 'color' => '#ffc107'],
                'final' => ['label' => 'Final', 'class' => 'badge-success', 'color' => '#28a745']
            ],
            'barang_keluar' => [
                'draft' => ['label' => 'Draft', 'class' => 'badge-warning', 'color' => '#ffc107'],
                'final' => ['label' => 'Final', 'class' => 'badge-success', 'color' => '#28a745']
            ],
            'stok_opname' => [
                'draft' => ['label' => 'Draft', 'class' => 'badge-warning', 'color' => '#ffc107'],
                'final' => ['label' => 'Final', 'class' => 'badge-success', 'color' => '#28a745']
            ],
            
            // Sales & Purchase Operations
            'pesanan' => [
                'draft' => ['label' => 'Draft', 'class' => 'badge-warning', 'color' => '#ffc107'],
                'confirmed' => ['label' => 'Dikonfirmasi', 'class' => 'badge-info', 'color' => '#17a2b8'],
                'shipped' => ['label' => 'Dikirim', 'class' => 'badge-primary', 'color' => '#6f42c1'],
                'completed' => ['label' => 'Selesai', 'class' => 'badge-success', 'color' => '#28a745'],
                'cancelled' => ['label' => 'Dibatalkan', 'class' => 'badge-danger', 'color' => '#dc3545']
            ],
            'pembelian' => [
                'draft' => ['label' => 'Draft', 'class' => 'badge-warning', 'color' => '#ffc107'],
                'approved' => ['label' => 'Disetujui', 'class' => 'badge-info', 'color' => '#17a2b8'],
                'ordered' => ['label' => 'Dipesan', 'class' => 'badge-primary', 'color' => '#007bff'],
                'received' => ['label' => 'Diterima', 'class' => 'badge-secondary', 'color' => '#6c757d'],
                'completed' => ['label' => 'Selesai', 'class' => 'badge-success', 'color' => '#28a745'],
                'cancelled' => ['label' => 'Dibatalkan', 'class' => 'badge-danger', 'color' => '#dc3545']
            ],
            
            // Shipping Operations
            'pengiriman' => [
                'prepared' => ['label' => 'Disiapkan', 'class' => 'badge-warning', 'color' => '#ffc107'],
                'shipped' => ['label' => 'Dikirim', 'class' => 'badge-primary', 'color' => '#6f42c1'],
                'in_transit' => ['label' => 'Dalam Perjalanan', 'class' => 'badge-info', 'color' => '#17a2b8'],
                'delivered' => ['label' => 'Diterima', 'class' => 'badge-success', 'color' => '#28a745'],
                'returned' => ['label' => 'Dikembalikan', 'class' => 'badge-danger', 'color' => '#dc3545']
            ],
            
            // Financial Operations
            'faktur_penjualan' => [
                'draft' => ['label' => 'Draft', 'class' => 'badge-warning', 'color' => '#ffc107'],
                'issued' => ['label' => 'Diterbitkan', 'class' => 'badge-info', 'color' => '#17a2b8'],
                'paid' => ['label' => 'Dibayar', 'class' => 'badge-success', 'color' => '#28a745'],
                'overdue' => ['label' => 'Terlambat', 'class' => 'badge-danger', 'color' => '#fd7e14'],
                'cancelled' => ['label' => 'Dibatalkan', 'class' => 'badge-secondary', 'color' => '#6c757d']
            ],
            'pembelian_pembayaran' => [
                'pending' => ['label' => 'Menunggu', 'class' => 'badge-warning', 'color' => '#ffc107'],
                'verified' => ['label' => 'Terverifikasi', 'class' => 'badge-success', 'color' => '#28a745'],
                'rejected' => ['label' => 'Ditolak', 'class' => 'badge-danger', 'color' => '#dc3545']
            ],
            
            // Transfer Operations
            'transfer_stok' => [
                'draft' => ['label' => 'Draft', 'class' => 'badge-warning', 'color' => '#ffc107'],
                'shipped' => ['label' => 'Dikirim', 'class' => 'badge-primary', 'color' => '#6f42c1'],
                'received' => ['label' => 'Diterima', 'class' => 'badge-success', 'color' => '#28a745'],
                'cancelled' => ['label' => 'Dibatalkan', 'class' => 'badge-danger', 'color' => '#dc3545']
            ]
        ];
    }
}

if (!function_exists('get_status_badge')) {
    /**
     * Generate status badge HTML
     * 
     * @param string $module
     * @param string $status
     * @return string
     */
    function get_status_badge($module, $status) {
        $config = get_status_config();
        
        if (!isset($config[$module][$status])) {
            return '<span class="badge badge-secondary">' . ucfirst($status) . '</span>';
        }
        
        $status_config = $config[$module][$status];
        return '<span class="badge ' . $status_config['class'] . '">' . $status_config['label'] . '</span>';
    }
}

if (!function_exists('get_status_label')) {
    /**
     * Get status label text
     * 
     * @param string $module
     * @param string $status
     * @return string
     */
    function get_status_label($module, $status) {
        $config = get_status_config();
        
        if (!isset($config[$module][$status])) {
            return ucfirst($status);
        }
        
        return $config[$module][$status]['label'];
    }
}

if (!function_exists('get_status_options')) {
    /**
     * Get status options for dropdown
     * 
     * @param string $module
     * @return array
     */
    function get_status_options($module) {
        $config = get_status_config();
        
        if (!isset($config[$module])) {
            return [];
        }
        
        $options = [];
        foreach ($config[$module] as $value => $data) {
            $options[$value] = $data['label'];
        }
        
        return $options;
    }
}

if (!function_exists('get_status_class')) {
    /**
     * Get status CSS class
     * 
     * @param string $module
     * @param string $status
     * @return string
     */
    function get_status_class($module, $status) {
        $config = get_status_config();
        
        if (!isset($config[$module][$status])) {
            return 'badge-secondary';
        }
        
        return $config[$module][$status]['class'];
    }
}

if (!function_exists('get_status_color')) {
    /**
     * Get status color
     * 
     * @param string $module
     * @param string $status
     * @return string
     */
    function get_status_color($module, $status) {
        $config = get_status_config();
        
        if (!isset($config[$module][$status])) {
            return '#6c757d';
        }
        
        return $config[$module][$status]['color'];
    }
}

if (!function_exists('validate_status')) {
    /**
     * Validate if status is valid for module
     * 
     * @param string $module
     * @param string $status
     * @return bool
     */
    function validate_status($module, $status) {
        $config = get_status_config();
        return isset($config[$module][$status]);
    }
}

if (!function_exists('get_status_transitions')) {
    /**
     * Get allowed status transitions for each module
     * 
     * @return array
     */
    function get_status_transitions() {
        return [
            'pesanan' => [
                'draft' => ['confirmed', 'cancelled'],
                'confirmed' => ['shipped', 'cancelled'],
                'shipped' => ['completed'],
                'completed' => [],
                'cancelled' => []
            ],
            'pembelian' => [
                'draft' => ['approved', 'cancelled'],
                'approved' => ['ordered', 'cancelled'],
                'ordered' => ['received', 'cancelled'],
                'received' => ['completed'],
                'completed' => [],
                'cancelled' => []
            ],
            'pengiriman' => [
                'prepared' => ['shipped', 'returned'],
                'shipped' => ['in_transit', 'returned'],
                'in_transit' => ['delivered', 'returned'],
                'delivered' => [],
                'returned' => []
            ],
            'faktur_penjualan' => [
                'draft' => ['issued', 'cancelled'],
                'issued' => ['paid', 'overdue', 'cancelled'],
                'paid' => [],
                'overdue' => ['paid', 'cancelled'],
                'cancelled' => []
            ],
            'transfer_stok' => [
                'draft' => ['shipped', 'cancelled'],
                'shipped' => ['received'],
                'received' => [],
                'cancelled' => []
            ],
            'barang_masuk' => [
                'draft' => ['final'],
                'final' => []
            ],
            'barang_keluar' => [
                'draft' => ['final'],
                'final' => []
            ],
            'stok_opname' => [
                'draft' => ['final'],
                'final' => []
            ]
        ];
    }
}

if (!function_calls('can_transition_status')) {
    /**
     * Check if status transition is allowed
     * 
     * @param string $module
     * @param string $from_status
     * @param string $to_status
     * @return bool
     */
    function can_transition_status($module, $from_status, $to_status) {
        $transitions = get_status_transitions();
        
        if (!isset($transitions[$module][$from_status])) {
            return false;
        }
        
        return in_array($to_status, $transitions[$module][$from_status]);
    }
}
