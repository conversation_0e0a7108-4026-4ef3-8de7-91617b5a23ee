<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Purchase Order - <?= $pembelian->nomor_pembelian ?></title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="<?= base_url('assets/plugins/bootstrap/css/bootstrap.min.css') ?>">
    
    <style>
        @media print {
            .no-print { display: none !important; }
            body { font-size: 12px; }
            .table { font-size: 11px; }
        }
        
        .header-company {
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .po-title {
            background-color: #007bff;
            color: white;
            padding: 10px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .info-box {
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .table-bordered th,
        .table-bordered td {
            border: 1px solid #000 !important;
        }
        
        .table thead th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .signature-box {
            border: 1px solid #ddd;
            height: 80px;
            margin-top: 10px;
        }
        
        .footer-note {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header Company -->
        <div class="header-company">
            <div class="row">
                <div class="col-md-8">
                    <h2 class="mb-1"><?= $this->session->userdata('nama_aplikasi') ?: 'Toko Elektronik' ?></h2>
                    <p class="mb-1">
                        <?php
                        $aplikasi = $this->db->get('aplikasi')->row();
                        if ($aplikasi) {
                            echo $aplikasi->alamat . '<br>';
                            echo 'Telp: ' . $aplikasi->tlp . '<br>';
                            echo 'Email: ' . $aplikasi->email;
                        }
                        ?>
                    </p>
                </div>
                <div class="col-md-4 text-right">
                    <h4>PURCHASE ORDER</h4>
                    <p class="mb-0">
                        <strong>No: <?= $pembelian->nomor_pembelian ?></strong><br>
                        Tanggal: <?= date('d/m/Y', strtotime($pembelian->tanggal_pembelian)) ?>
                    </p>
                </div>
            </div>
        </div>

        <!-- Supplier Info -->
        <div class="row">
            <div class="col-md-6">
                <div class="info-box">
                    <h5 class="mb-3"><strong>SUPPLIER:</strong></h5>
                    <p class="mb-1"><strong><?= $pembelian->nama_supplier ?></strong></p>
                    <p class="mb-1"><?= $pembelian->alamat_supplier ?></p>
                    <p class="mb-1">Telp: <?= $pembelian->telepon_supplier ?></p>
                    <p class="mb-0">Email: <?= $pembelian->email_supplier ?></p>
                </div>
            </div>
            <div class="col-md-6">
                <div class="info-box">
                    <h5 class="mb-3"><strong>SHIP TO:</strong></h5>
                    <p class="mb-0"><?= $pembelian->alamat_pengiriman ?: $aplikasi->alamat ?></p>
                </div>
            </div>
        </div>

        <!-- PO Details -->
        <div class="row mb-3">
            <div class="col-md-6">
                <table class="table table-borderless table-sm">
                    <tr>
                        <td width="40%"><strong>Jenis Pembelian</strong></td>
                        <td>: <?= ucfirst($pembelian->jenis_pembelian) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Syarat Pembayaran</strong></td>
                        <td>: <?= $pembelian->syarat_pembayaran ?: '-' ?></td>
                    </tr>
                    <tr>
                        <td><strong>Metode Pembayaran</strong></td>
                        <td>: <?= ucfirst($pembelian->metode_pembayaran) ?></td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <table class="table table-borderless table-sm">
                    <tr>
                        <td width="40%"><strong>Tanggal Jatuh Tempo</strong></td>
                        <td>: <?= $pembelian->tanggal_jatuh_tempo ? date('d/m/Y', strtotime($pembelian->tanggal_jatuh_tempo)) : '-' ?></td>
                    </tr>
                    <tr>
                        <td><strong>No. PO Supplier</strong></td>
                        <td>: <?= $pembelian->nomor_po_supplier ?: '-' ?></td>
                    </tr>
                    <tr>
                        <td><strong>Status</strong></td>
                        <td>: <?= ucfirst($pembelian->status) ?></td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Items Table -->
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr class="bg-light">
                        <th width="5%" class="text-center">No</th>
                        <th width="15%">Kode Barang</th>
                        <th width="25%">Nama Barang</th>
                        <th width="10%">Gudang</th>
                        <th width="8%" class="text-center">Qty</th>
                        <th width="5%">Satuan</th>
                        <th width="12%" class="text-right">Harga Satuan</th>
                        <th width="8%" class="text-center">Diskon</th>
                        <th width="12%" class="text-right">Total</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($detail)): ?>
                        <?php 
                        $no = 1; 
                        $total_qty = 0;
                        $grand_total = 0;
                        foreach ($detail as $item): 
                            $total_qty += $item->qty;
                            $grand_total += $item->total_akhir;
                        ?>
                        <tr>
                            <td class="text-center"><?= $no++ ?></td>
                            <td><?= $item->kode_barang ?></td>
                            <td>
                                <?= $item->nama_barang ?>
                                <?php if ($item->merk): ?><br><small class="text-muted"><?= $item->merk ?></small><?php endif; ?>
                                <?php if ($item->keterangan): ?><br><small class="text-muted"><?= $item->keterangan ?></small><?php endif; ?>
                            </td>
                            <td><?= $item->nama_gudang ?></td>
                            <td class="text-center"><?= number_format($item->qty, 0) ?></td>
                            <td><?= $item->nama_satuan ?></td>
                            <td class="text-right">Rp <?= number_format($item->harga_satuan, 0, ',', '.') ?></td>
                            <td class="text-center">
                                <?php if ($item->diskon_persen > 0): ?>
                                    <?= $item->diskon_persen ?>%
                                <?php elseif ($item->diskon_nominal > 0): ?>
                                    Rp <?= number_format($item->diskon_nominal, 0, ',', '.') ?>
                                <?php else: ?>
                                    -
                                <?php endif; ?>
                            </td>
                            <td class="text-right">Rp <?= number_format($item->total_akhir, 0, ',', '.') ?></td>
                        </tr>
                        <?php endforeach; ?>
                        
                        <!-- Summary Row -->
                        <tr class="bg-light">
                            <td colspan="4" class="text-right"><strong>TOTAL</strong></td>
                            <td class="text-center"><strong><?= number_format($total_qty, 0) ?></strong></td>
                            <td colspan="3" class="text-right"><strong>Subtotal:</strong></td>
                            <td class="text-right"><strong>Rp <?= number_format($pembelian->subtotal, 0, ',', '.') ?></strong></td>
                        </tr>
                        
                        <?php if ($pembelian->diskon_nominal > 0): ?>
                        <tr>
                            <td colspan="8" class="text-right"><strong>Diskon:</strong></td>
                            <td class="text-right"><strong>Rp <?= number_format($pembelian->diskon_nominal, 0, ',', '.') ?></strong></td>
                        </tr>
                        <?php endif; ?>
                        
                        <tr>
                            <td colspan="8" class="text-right"><strong>PPN (<?= $pembelian->ppn_persen ?>%):</strong></td>
                            <td class="text-right"><strong>Rp <?= number_format($pembelian->ppn_nominal, 0, ',', '.') ?></strong></td>
                        </tr>
                        
                        <?php if ($pembelian->biaya_pengiriman > 0): ?>
                        <tr>
                            <td colspan="8" class="text-right"><strong>Biaya Pengiriman:</strong></td>
                            <td class="text-right"><strong>Rp <?= number_format($pembelian->biaya_pengiriman, 0, ',', '.') ?></strong></td>
                        </tr>
                        <?php endif; ?>
                        
                        <tr class="bg-primary text-white">
                            <td colspan="8" class="text-right"><strong>GRAND TOTAL:</strong></td>
                            <td class="text-right"><strong>Rp <?= number_format($pembelian->total_akhir, 0, ',', '.') ?></strong></td>
                        </tr>
                    <?php else: ?>
                        <tr>
                            <td colspan="9" class="text-center">Tidak ada item</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Notes -->
        <?php if ($pembelian->keterangan): ?>
        <div class="row">
            <div class="col-md-12">
                <div class="info-box">
                    <h6><strong>Catatan:</strong></h6>
                    <p class="mb-0"><?= nl2br($pembelian->keterangan) ?></p>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Signature Section -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="text-center">
                    <p class="mb-1"><strong>Dibuat Oleh:</strong></p>
                    <div class="signature-box"></div>
                    <p class="mt-2 mb-0"><?= $pembelian->created_by ?></p>
                    <small>Tanggal: <?= date('d/m/Y', strtotime($pembelian->created_at)) ?></small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center">
                    <p class="mb-1"><strong>Disetujui Oleh:</strong></p>
                    <div class="signature-box"></div>
                    <p class="mt-2 mb-0"><?= $pembelian->approved_by ?: '________________' ?></p>
                    <small>Tanggal: <?= $pembelian->approved_at ? date('d/m/Y', strtotime($pembelian->approved_at)) : '________________' ?></small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center">
                    <p class="mb-1"><strong>Diterima Supplier:</strong></p>
                    <div class="signature-box"></div>
                    <p class="mt-2 mb-0">________________</p>
                    <small>Tanggal: ________________</small>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer-note">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">
                        <strong>Syarat dan Ketentuan:</strong><br>
                        1. Barang yang dikirim harus sesuai dengan spesifikasi yang diminta<br>
                        2. Pembayaran dilakukan sesuai dengan syarat pembayaran yang disepakati<br>
                        3. Supplier wajib memberikan garansi sesuai dengan ketentuan yang berlaku
                    </p>
                </div>
                <div class="col-md-6 text-right">
                    <p class="mb-0">
                        <small>
                            Dokumen ini dicetak pada: <?= date('d/m/Y H:i:s') ?><br>
                            Purchase Order No: <?= $pembelian->nomor_pembelian ?>
                        </small>
                    </p>
                </div>
            </div>
        </div>

        <!-- Print Button -->
        <div class="row no-print mt-3">
            <div class="col-md-12 text-center">
                <button type="button" class="btn btn-primary" onclick="window.print()">
                    <i class="fas fa-print"></i> Print
                </button>
                <button type="button" class="btn btn-secondary ml-2" onclick="window.close()">
                    <i class="fas fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="<?= base_url('assets/plugins/jquery/jquery.min.js') ?>"></script>
    <script src="<?= base_url('assets/plugins/bootstrap/js/bootstrap.bundle.min.js') ?>"></script>
    <script>
        // Auto print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>