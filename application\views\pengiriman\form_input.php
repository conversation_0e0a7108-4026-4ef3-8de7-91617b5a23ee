<form action="#" id="form" class="form-horizontal">
    <input type="hidden" value="" name="id" />
    <div class="card-body">

        <!-- Tab Navigation -->
        <ul class="nav nav-tabs" id="pengirimanTab" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" id="basic-tab" data-toggle="tab" href="#basic" role="tab">Data Dasar</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="pesanan-tab" data-toggle="tab" href="#pesanan" role="tab">Pesanan & Pelanggan</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="ekspedisi-tab" data-toggle="tab" href="#ekspedisi" role="tab">Ekspedisi & Tracking</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="keterangan-tab" data-toggle="tab" href="#keterangan" role="tab">Keterangan</a>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="pengirimanTabContent">

            <!-- Tab Data Dasar -->
            <div class="tab-pane fade show active" id="basic" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="nomor_pengiriman" class="col-sm-3 col-form-label">Nomor Pengiriman <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <div class="input-group">
                                <input type="text" class="form-control" name="nomor_pengiriman" id="nomor_pengiriman" 
                                       placeholder="Nomor akan digenerate otomatis" value="<?= $nomor_pengiriman ?>" readonly>
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-outline-secondary" id="btn-generate-nomor" onclick="generateNomor()">
                                        <i class="fa fa-sync"></i> Generate
                                    </button>
                                </div>
                            </div>
                            <small class="form-text text-muted">Nomor pengiriman akan digenerate otomatis jika kosong.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="tanggal_pengiriman" class="col-sm-3 col-form-label">Tanggal Pengiriman <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <input type="date" class="form-control" name="tanggal_pengiriman" id="tanggal_pengiriman" required>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="status" class="col-sm-3 col-form-label">Status</label>
                        <div class="col-sm-9 kosong">
                            <select class="form-control select2" name="status" id="status" style="width: 100%;">
                                <option value="draft">Draft</option>
                                <option value="prepared">Prepared</option>
                                <option value="shipped">Shipped</option>
                                <option value="in_transit">In Transit</option>
                                <option value="delivered">Delivered</option>
                                <option value="returned">Returned</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                            <small class="form-text text-muted">Status pengiriman saat ini.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Tab Pesanan & Pelanggan -->
            <div class="tab-pane fade" id="pesanan" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="id_pesanan" class="col-sm-3 col-form-label">Pesanan <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <select class="form-control select2" name="id_pesanan" id="id_pesanan" style="width: 100%;" required>
                                <option value="">-- Pilih Pesanan --</option>
                                <?php if (isset($pesanan_list) && is_array($pesanan_list)): ?>
                                    <?php foreach ($pesanan_list as $pesanan): ?>
                                        <option value="<?= $pesanan->id ?>"
                                                data-pelanggan="<?= $pesanan->nama_pelanggan ?>"
                                                data-alamat="<?= $pesanan->alamat_pelanggan ?>"
                                                data-telepon="<?= $pesanan->no_telepon ?>">
                                            <?= $pesanan->nomor_pesanan ?> - <?= $pesanan->nama_pelanggan ?>
                                            (<?= date('d/m/Y', strtotime($pesanan->tanggal_pesanan)) ?>)
                                        </option>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <option value="" disabled>Tidak ada pesanan yang tersedia</option>
                                <?php endif; ?>
                            </select>
                            <small class="form-text text-muted">Pilih pesanan yang akan dikirim. Hanya pesanan dengan status 'diproses' yang ditampilkan.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="id_pelanggan" class="col-sm-3 col-form-label">Pelanggan <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <select class="form-control select2" name="id_pelanggan" id="id_pelanggan" style="width: 100%;" required>
                                <option value="">-- Pilih Pelanggan --</option>
                                <?php if (isset($pelanggan_list) && is_array($pelanggan_list)): ?>
                                    <?php foreach ($pelanggan_list as $pelanggan): ?>
                                        <option value="<?= $pelanggan->id ?>"><?= $pelanggan->kode ?> - <?= $pelanggan->nama ?></option>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <option value="" disabled>Tidak ada pelanggan yang tersedia</option>
                                <?php endif; ?>
                            </select>
                            <small class="form-text text-muted">Pelanggan akan terisi otomatis saat memilih pesanan.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="alamat_pengiriman" class="col-sm-3 col-form-label">Alamat Pengiriman <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <textarea class="form-control" name="alamat_pengiriman" id="alamat_pengiriman" 
                                      placeholder="Alamat lengkap untuk pengiriman" rows="3" required></textarea>
                            <small class="form-text text-muted">Alamat akan terisi otomatis dari data pelanggan, dapat diubah sesuai kebutuhan.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Tab Ekspedisi & Tracking -->
            <div class="tab-pane fade" id="ekspedisi" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="ekspedisi" class="col-sm-3 col-form-label">Ekspedisi</label>
                        <div class="col-sm-9 kosong">
                            <input type="text" class="form-control" name="ekspedisi" id="ekspedisi" 
                                   placeholder="Nama ekspedisi (JNE, TIKI, POS, dll)">
                            <small class="form-text text-muted">Nama perusahaan ekspedisi yang digunakan.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="nomor_resi" class="col-sm-3 col-form-label">Nomor Resi</label>
                        <div class="col-sm-9 kosong">
                            <input type="text" class="form-control" name="nomor_resi" id="nomor_resi" 
                                   placeholder="Nomor resi dari ekspedisi">
                            <small class="form-text text-muted">Nomor resi untuk tracking pengiriman.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="biaya_pengiriman" class="col-sm-3 col-form-label">Biaya Pengiriman</label>
                        <div class="col-sm-9 kosong">
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">Rp</span>
                                </div>
                                <input type="number" class="form-control" name="biaya_pengiriman" id="biaya_pengiriman" 
                                       placeholder="0" min="0" step="0.01">
                            </div>
                            <small class="form-text text-muted">Biaya pengiriman yang dikenakan.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="estimasi_tiba" class="col-sm-3 col-form-label">Estimasi Tiba</label>
                        <div class="col-sm-9 kosong">
                            <input type="date" class="form-control" name="estimasi_tiba" id="estimasi_tiba">
                            <small class="form-text text-muted">Perkiraan tanggal barang sampai di tujuan.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Tab Keterangan -->
            <div class="tab-pane fade" id="keterangan" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="keterangan" class="col-sm-3 col-form-label">Keterangan</label>
                        <div class="col-sm-9 kosong">
                            <textarea class="form-control" name="keterangan" id="keterangan" 
                                      placeholder="Keterangan tambahan mengenai pengiriman ini (opsional)" rows="4"></textarea>
                            <small class="form-text text-muted">Informasi tambahan mengenai pengiriman ini.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                </div>
            </div>

        </div>

    </div>
</form>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        dropdownParent: $('#modal_form'),
        placeholder: "-- Pilih --",
        allowClear: true
    });

    // Set tanggal hari ini sebagai default
    var today = new Date().toISOString().split('T')[0];
    $('#tanggal_pengiriman').val(today);

    // Event handler untuk perubahan pesanan
    $('#id_pesanan').on('change', function() {
        var selectedOption = $(this).find('option:selected');
        var pelangganNama = selectedOption.data('pelanggan');
        var alamat = selectedOption.data('alamat');
        var telepon = selectedOption.data('telepon');

        if (pelangganNama) {
            // Find pelanggan ID by name and set it
            $('#id_pelanggan option').each(function() {
                if ($(this).text().includes(pelangganNama)) {
                    $('#id_pelanggan').val($(this).val()).trigger('change');
                    return false;
                }
            });
            
            // Set alamat pengiriman
            if (alamat) {
                $('#alamat_pengiriman').val(alamat);
            }
        }
    });
});

function generateNomor() {
    $.ajax({
        url: "<?php echo site_url('pengiriman/generate_nomor') ?>",
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            $('#nomor_pengiriman').val(data.nomor);
        },
        error: function(jqXHR, textStatus, errorThrown) {
            Swal.fire({
                title: 'Error!',
                text: 'Terjadi kesalahan saat generate nomor.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    });
}
</script>
