<form action="#" id="form_detail_item" class="form-horizontal">
    <input type="hidden" name="id" value="">
    <input type="hidden" name="id_pembelian" value="">
    
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label for="id_barang" class="control-label">Barang <span class="text-red">*</span></label>
                <select class="form-control select2" id="id_barang" name="id_barang" style="width: 100%;" required>
                    <option value="">-- Pilih Barang --</option>
                    <?php foreach ($barang_list as $barang): ?>
                        <option value="<?= $barang->id ?>" data-harga="<?= $barang->harga_beli ?>">
                            <?= $barang->kode_barang ?> - <?= $barang->nama_barang ?>
                            <?php if ($barang->merk): ?>(<?= $barang->merk ?>)<?php endif; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <span class="help-block"></span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="id_gudang" class="control-label">Gudang <span class="text-red">*</span></label>
                <select class="form-control select2" id="id_gudang" name="id_gudang" style="width: 100%;" required>
                    <option value="">-- Pilih Gudang --</option>
                    <?php foreach ($gudang_list as $gudang): ?>
                        <option value="<?= $gudang->id ?>"><?= $gudang->kode_gudang ?> - <?= $gudang->nama_gudang ?></option>
                    <?php endforeach; ?>
                </select>
                <span class="help-block"></span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="form-group">
                <label for="qty" class="control-label">Quantity <span class="text-red">*</span></label>
                <input type="number" class="form-control" id="qty" name="qty" 
                       placeholder="0" min="0" step="1" required>
                <span class="help-block"></span>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label for="harga_satuan" class="control-label">Harga Satuan <span class="text-red">*</span></label>
                <input type="number" class="form-control" id="harga_satuan" name="harga_satuan" 
                       placeholder="0" min="0" step="0.01" required>
                <span class="help-block"></span>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label for="subtotal_display" class="control-label">Subtotal</label>
                <input type="text" class="form-control" id="subtotal_display" 
                       placeholder="Rp 0" readonly>
                <span class="help-block"></span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="form-group">
                <label for="diskon_persen" class="control-label">Diskon (%)</label>
                <input type="number" class="form-control" id="diskon_persen" name="diskon_persen" 
                       placeholder="0" min="0" max="100" step="0.01">
                <span class="help-block"></span>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label for="diskon_nominal" class="control-label">Diskon (Rp)</label>
                <input type="number" class="form-control" id="diskon_nominal" name="diskon_nominal" 
                       placeholder="0" min="0" step="0.01">
                <span class="help-block"></span>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label for="ppn_persen" class="control-label">PPN (%)</label>
                <input type="number" class="form-control" id="ppn_persen" name="ppn_persen" 
                       placeholder="11" value="11" min="0" max="100" step="0.01">
                <span class="help-block"></span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label for="total_setelah_diskon_display" class="control-label">Total Setelah Diskon</label>
                <input type="text" class="form-control" id="total_setelah_diskon_display" 
                       placeholder="Rp 0" readonly>
                <span class="help-block"></span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="total_akhir_display" class="control-label">Total Akhir (+ PPN)</label>
                <input type="text" class="form-control" id="total_akhir_display" 
                       placeholder="Rp 0" readonly style="font-weight: bold;">
                <span class="help-block"></span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="form-group">
                <label for="keterangan" class="control-label">Keterangan</label>
                <textarea class="form-control" id="keterangan" name="keterangan" rows="3" 
                          placeholder="Keterangan tambahan untuk item ini"></textarea>
                <span class="help-block"></span>
            </div>
        </div>
    </div>
</form>

<script>
$(document).ready(function() {
    $('.select2').select2({
        dropdownParent: $('#modal_detail_item')
    });

    // Event handler untuk perubahan barang
    $('#id_barang').on('change', function() {
        var selectedOption = $(this).find('option:selected');
        var hargaBeli = selectedOption.data('harga');
        if (hargaBeli) {
            $('#harga_satuan').val(hargaBeli);
            calculateTotal();
        }
    });

    // Event handlers untuk perhitungan otomatis
    $('#qty, #harga_satuan, #diskon_persen, #diskon_nominal, #ppn_persen').on('input', function() {
        calculateTotal();
    });

    // Fungsi untuk menghitung total
    function calculateTotal() {
        var qty = parseFloat($('#qty').val()) || 0;
        var hargaSatuan = parseFloat($('#harga_satuan').val()) || 0;
        var diskonPersen = parseFloat($('#diskon_persen').val()) || 0;
        var diskonNominal = parseFloat($('#diskon_nominal').val()) || 0;
        var ppnPersen = parseFloat($('#ppn_persen').val()) || 0;

        // Hitung subtotal
        var subtotal = qty * hargaSatuan;
        $('#subtotal_display').val(formatRupiah(subtotal));

        // Hitung diskon
        var totalDiskon = 0;
        if (diskonPersen > 0) {
            totalDiskon = (subtotal * diskonPersen) / 100;
            // Update diskon nominal berdasarkan persen
            $('#diskon_nominal').val(totalDiskon);
        } else if (diskonNominal > 0) {
            totalDiskon = diskonNominal;
            // Update diskon persen berdasarkan nominal
            if (subtotal > 0) {
                var persenFromNominal = (diskonNominal / subtotal) * 100;
                $('#diskon_persen').val(persenFromNominal.toFixed(2));
            }
        }

        // Hitung total setelah diskon
        var totalSetelahDiskon = subtotal - totalDiskon;
        $('#total_setelah_diskon_display').val(formatRupiah(totalSetelahDiskon));

        // Hitung PPN
        var ppnNominal = (totalSetelahDiskon * ppnPersen) / 100;

        // Hitung total akhir
        var totalAkhir = totalSetelahDiskon + ppnNominal;
        $('#total_akhir_display').val(formatRupiah(totalAkhir));
    }

    // Fungsi untuk format rupiah
    function formatRupiah(angka) {
        var number_string = angka.toString().replace(/[^,\d]/g, '');
        var split = number_string.split(',');
        var sisa = split[0].length % 3;
        var rupiah = split[0].substr(0, sisa);
        var ribuan = split[0].substr(sisa).match(/\d{3}/gi);

        if (ribuan) {
            var separator = sisa ? '.' : '';
            rupiah += separator + ribuan.join('.');
        }

        rupiah = split[1] != undefined ? rupiah + ',' + split[1] : rupiah;
        return 'Rp ' + rupiah;
    }

    // Prevent form submission on enter
    $('#form_detail_item').on('keypress', function(e) {
        if (e.which == 13) {
            e.preventDefault();
            return false;
        }
    });

    // Sinkronisasi diskon persen dan nominal
    $('#diskon_persen').on('input', function() {
        if ($(this).val()) {
            $('#diskon_nominal').val('');
        }
    });

    $('#diskon_nominal').on('input', function() {
        if ($(this).val()) {
            $('#diskon_persen').val('');
        }
    });
});
</script>