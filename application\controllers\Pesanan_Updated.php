<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * <PERSON><PERSON>an Controller - Updated with Status Standardization
 * 
 * This is an example of how to implement the new status helper
 * in the Pesanan controller for consistent status handling
 */

class Pesanan_Updated extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->model('Mod_pesanan');
        $this->load->helper('status'); // Load the new status helper
        
        if (!$this->session->userdata('nama_user')) {
            redirect('auth');
        }
    }

    public function index()
    {
        $data['title'] = 'Data Pesanan';
        $this->load->view('pesanan/pesanan', $data);
    }

    public function ajax_list()
    {
        ini_set('memory_limit', '512M');
        set_time_limit(3600);
        $list = $this->Mod_pesanan->get_datatables();
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $pesanan) {
            $no++;
            $row = array();
            $row[] = $no;
            $row[] = $pesanan->nomor_pesanan;
            $row[] = date('d/m/Y', strtotime($pesanan->tanggal_pesanan));
            $row[] = $pesanan->nama_pelanggan;
            
            // Jenis badge
            if ($pesanan->jenis_pesanan == 'android') {
                $jenis_badge = '<span class="badge badge-info">Android</span>';
            } else {
                $jenis_badge = '<span class="badge badge-secondary">Manual</span>';
            }
            $row[] = $jenis_badge;
            
            // Status badge using new helper
            $row[] = get_status_badge('pesanan', $pesanan->status);
            
            $row[] = number_format($pesanan->total_item ?? 0, 0) . ' item';
            $row[] = number_format($pesanan->total_qty ?? 0, 0);
            $row[] = 'Rp ' . number_format($pesanan->total_harga ?? 0, 0, ',', '.');
            
            // Action buttons with status-aware logic
            $actions = $this->_generate_action_buttons($pesanan);
            $row[] = $actions;
            
            $data[] = $row;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_pesanan->count_all(),
            "recordsFiltered" => $this->Mod_pesanan->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    /**
     * Generate action buttons based on status and transitions
     */
    private function _generate_action_buttons($pesanan)
    {
        $actions = '';
        $transitions = get_status_transitions();
        $allowed_transitions = $transitions['pesanan'][$pesanan->status] ?? [];
        
        // Always show detail button
        $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $pesanan->id . ')"><i class="fas fa-list"></i></a> ';
        
        // Edit button only for draft status
        if ($pesanan->status == 'draft') {
            $actions .= '<a class="btn btn-xs btn-outline-info edit" href="javascript:void(0)" title="Edit" onclick="edit(' . $pesanan->id . ')"><i class="fas fa-edit"></i></a> ';
        }
        
        // Status transition buttons
        foreach ($allowed_transitions as $next_status) {
            $status_config = get_status_config()['pesanan'][$next_status];
            $button_class = $this->_get_button_class_for_status($next_status);
            $icon = $this->_get_icon_for_status($next_status);
            
            $actions .= '<a class="btn btn-xs ' . $button_class . '" href="javascript:void(0)" title="' . $status_config['label'] . '" onclick="updateStatus(' . $pesanan->id . ', \'' . $next_status . '\')"><i class="' . $icon . '"></i></a> ';
        }
        
        // Print button for non-draft status
        if ($pesanan->status != 'draft') {
            $actions .= '<a class="btn btn-xs btn-outline-info print" href="javascript:void(0)" title="Print" onclick="printPesanan(' . $pesanan->id . ')"><i class="fas fa-print"></i></a> ';
        }
        
        // Delete button only for draft status
        if ($pesanan->status == 'draft') {
            $actions .= '<a class="btn btn-xs btn-outline-danger delete" href="javascript:void(0)" title="Delete" onclick="hapus(' . $pesanan->id . ')"><i class="fas fa-trash"></i></a>';
        }
        
        return $actions;
    }

    /**
     * Get button class for status transition
     */
    private function _get_button_class_for_status($status)
    {
        $classes = [
            'confirmed' => 'btn-outline-success',
            'shipped' => 'btn-outline-primary',
            'completed' => 'btn-outline-info',
            'cancelled' => 'btn-outline-danger'
        ];
        
        return $classes[$status] ?? 'btn-outline-secondary';
    }

    /**
     * Get icon for status transition
     */
    private function _get_icon_for_status($status)
    {
        $icons = [
            'confirmed' => 'fas fa-check',
            'shipped' => 'fas fa-shipping-fast',
            'completed' => 'fas fa-check-double',
            'cancelled' => 'fas fa-times'
        ];
        
        return $icons[$status] ?? 'fas fa-arrow-right';
    }

    public function form_input()
    {
        $data['pelanggan_list'] = $this->Mod_pesanan->get_pelanggan_aktif();
        $data['nomor_pesanan'] = $this->Mod_pesanan->generate_nomor_pesanan();
        
        // Get status options using helper
        $data['status_options'] = get_status_options('pesanan');
        
        $this->load->view('pesanan/form_input_updated', $data);
    }

    public function update_status()
    {
        $id = $this->input->post('id');
        $new_status = $this->input->post('status');
        
        // Get current pesanan data
        $pesanan = $this->Mod_pesanan->get_by_id($id);
        if (!$pesanan) {
            echo json_encode(array('status' => 'error', 'message' => 'Pesanan tidak ditemukan!'));
            return;
        }
        
        // Validate status transition
        if (!can_transition_status('pesanan', $pesanan->status, $new_status)) {
            $current_label = get_status_label('pesanan', $pesanan->status);
            $new_label = get_status_label('pesanan', $new_status);
            echo json_encode(array(
                'status' => 'error', 
                'message' => "Tidak dapat mengubah status dari {$current_label} ke {$new_label}!"
            ));
            return;
        }
        
        // Validate status value
        if (!validate_status('pesanan', $new_status)) {
            echo json_encode(array('status' => 'error', 'message' => 'Status tidak valid!'));
            return;
        }
        
        $data = array(
            'status' => $new_status,
            'updated_by' => $this->session->userdata('nama_user'),
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        $update = $this->Mod_pesanan->update($id, $data);
        if ($update) {
            $status_label = get_status_label('pesanan', $new_status);
            echo json_encode(array(
                'status' => 'success', 
                'message' => "Status pesanan berhasil diubah menjadi {$status_label}!"
            ));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Status pesanan gagal diupdate!'));
        }
    }

    public function insert()
    {
        $this->_validate();

        $nomor = $this->input->post('nomor_pesanan');
        if (empty($nomor)) {
            $nomor = $this->Mod_pesanan->generate_nomor_pesanan();
        }

        $status = $this->input->post('status') ?: 'draft';
        
        // Validate status
        if (!validate_status('pesanan', $status)) {
            echo json_encode(array("status" => FALSE, "message" => "Status tidak valid!"));
            return;
        }

        $save = array(
            'nomor_pesanan' => $nomor,
            'tanggal_pesanan' => $this->input->post('tanggal_pesanan'),
            'id_pelanggan' => $this->input->post('id_pelanggan'),
            'jenis_pesanan' => $this->input->post('jenis_pesanan'),
            'status' => $status,
            'keterangan' => $this->input->post('keterangan'),
            'total_item' => 0,
            'total_qty' => 0,
            'total_harga' => 0,
            'created_by' => $this->session->userdata('nama_user'),
        );

        $this->Mod_pesanan->insert('pesanan', $save);
        echo json_encode(array("status" => TRUE));
    }

    public function update()
    {
        $this->_validate();

        $id = $this->input->post('id');
        $status = $this->input->post('status');
        
        // Validate status
        if (!validate_status('pesanan', $status)) {
            echo json_encode(array("status" => FALSE, "message" => "Status tidak valid!"));
            return;
        }
        
        // Get current data for transition validation
        $current_pesanan = $this->Mod_pesanan->get_by_id($id);
        if ($current_pesanan && !can_transition_status('pesanan', $current_pesanan->status, $status)) {
            $current_label = get_status_label('pesanan', $current_pesanan->status);
            $new_label = get_status_label('pesanan', $status);
            echo json_encode(array(
                "status" => FALSE, 
                "message" => "Tidak dapat mengubah status dari {$current_label} ke {$new_label}!"
            ));
            return;
        }

        $save = array(
            'tanggal_pesanan' => $this->input->post('tanggal_pesanan'),
            'id_pelanggan' => $this->input->post('id_pelanggan'),
            'jenis_pesanan' => $this->input->post('jenis_pesanan'),
            'status' => $status,
            'keterangan' => $this->input->post('keterangan'),
            'updated_by' => $this->session->userdata('nama_user'),
        );

        $this->Mod_pesanan->update($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function delete()
    {
        $id = $this->input->post('id');

        // Cek apakah pesanan masih draft
        $pesanan = $this->Mod_pesanan->get_by_id($id);
        if($pesanan && $pesanan->status != 'draft') {
            $status_label = get_status_label('pesanan', $pesanan->status);
            echo json_encode(array(
                'status' => FALSE, 
                'message' => "Pesanan dengan status {$status_label} tidak dapat dihapus!"
            ));
            return;
        }

        // Hapus detail pesanan terlebih dahulu
        $this->Mod_pesanan->delete_detail_by_pesanan($id);

        // Hapus pesanan
        $delete_result = $this->Mod_pesanan->delete($id, 'pesanan');
        
        if($delete_result) {
            echo json_encode(array(
                "status" => 'success', 
                "message" => 'Data pesanan berhasil dihapus.'
            ));
        } else {
            echo json_encode(array(
                "status" => 'error', 
                "message" => 'Gagal menghapus data pesanan.'
            ));
        }
    }

    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;
        
        if($this->input->post('nomor_pesanan') == '')
        {
            $data['inputerror'][] = 'nomor_pesanan';
            $data['error_string'][] = 'Nomor pesanan tidak boleh kosong';
            $data['status'] = FALSE;
        }
        
        if($this->input->post('tanggal_pesanan') == '')
        {
            $data['inputerror'][] = 'tanggal_pesanan';
            $data['error_string'][] = 'Tanggal pesanan tidak boleh kosong';
            $data['status'] = FALSE;
        }
        
        if($this->input->post('id_pelanggan') == '')
        {
            $data['inputerror'][] = 'id_pelanggan';
            $data['error_string'][] = 'Pelanggan harus dipilih';
            $data['status'] = FALSE;
        }
        
        if($this->input->post('jenis_pesanan') == '')
        {
            $data['inputerror'][] = 'jenis_pesanan';
            $data['error_string'][] = 'Jenis pesanan harus dipilih';
            $data['status'] = FALSE;
        }
        
        if($data['status'] === FALSE)
        {
            echo json_encode($data);
            exit();
        }
    }

    // Other methods remain the same...
    // add_detail(), update_detail(), delete_detail(), etc.
}
