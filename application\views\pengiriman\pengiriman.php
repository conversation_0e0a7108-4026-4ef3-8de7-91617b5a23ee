<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fa fa-shipping-fast text-blue"></i> Data Pengiriman</h3>
                        <div class="text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary add" onclick="add()" title="Add Data"><i class="fas fa-plus"></i> Add</button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <?= $this->session->flashdata('pesan') ?>
                        <table id="tbl_pengiriman" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr class="bg-info">
                                    <th>Nomor <PERSON></th>
                                    <th><PERSON>gal</th>
                                    <th>P<PERSON><PERSON><PERSON></th>
                                    <th><PERSON><PERSON></th>
                                    <th>Ekspedisi</th>
                                    <th>Nomor Resi</th>
                                    <th>Status</th>
                                    <th>Total Item</th>
                                    <th>Total Qty</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->

<!-- Modal Form -->
<div class="modal fade" id="modal_form" role="dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <h4 class="modal-title text-white">Form Pengiriman</h4>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSave" onclick="save()" class="btn btn-primary">
                    <i class="fa fa-save"></i> Simpan
                </button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fa fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detail -->
<div class="modal fade" id="modal_detail" role="dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-info">
                <h4 class="modal-title text-white">Detail Pengiriman</h4>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body-detail">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fa fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detail Pengiriman -->
<div class="modal fade" id="modal_detail" role="dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <h4 class="modal-title text-white">Detail Pengiriman</h4>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body-detail">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fa fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    var table;
    var save_method;
    var id_pengiriman;

    $(document).ready(function() {
        $('.select2').select2();
        
        //datatables
        table = $("#tbl_pengiriman").DataTable({
            "responsive": true,
            "autoWidth": false,
            "language": {
                "sEmptyTable": "Data Pengiriman Belum Ada"
            },
            "processing": true, //Feature control the processing indicator.
            "serverSide": true, //Feature control DataTables' server-side processing mode.
            "order": [], //Initial no order.

            // Load data for the table's content from an Ajax source
            "ajax": {
                "url": "<?php echo site_url('pengiriman/ajax_list') ?>",
                "type": "POST"
            },

            //Set column definition initialisation properties.
            "columnDefs": [{
                "targets": [-1], //last column
                "orderable": false, //set not orderable
            }],
        });
    });

    function add() {
        save_method = 'add';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('.modal-title').text('Tambah Pengiriman'); // set a title to modal header

        // Load form add
        $.ajax({
            url: "<?php echo site_url('pengiriman/form_input') ?>",
            type: "GET",
            dataType: "HTML",
            timeout: 10000, // 10 second timeout
            success: function(data) {
                if (data && data.trim().length > 0) {
                    $('#modal-body').html(data);
                    $('#modal_form').modal('show'); // show bootstrap modal after loading form
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Form kosong atau tidak dapat dimuat.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log('AJAX Error:', {
                    status: jqXHR.status,
                    statusText: jqXHR.statusText,
                    responseText: jqXHR.responseText,
                    textStatus: textStatus,
                    errorThrown: errorThrown
                });

                let errorMessage = 'Terjadi kesalahan saat memuat form.';
                if (jqXHR.status === 500) {
                    errorMessage = 'Server Error (500). Periksa log server untuk detail.';
                } else if (jqXHR.status === 404) {
                    errorMessage = 'Form tidak ditemukan (404).';
                } else if (jqXHR.status === 0) {
                    errorMessage = 'Tidak dapat terhubung ke server.';
                }

                Swal.fire({
                    title: 'Error!',
                    text: errorMessage,
                    icon: 'error',
                    confirmButtonText: 'OK',
                    footer: 'Status: ' + jqXHR.status + ' - ' + textStatus
                });
            }
        });
    }

    function edit(id) {
        save_method = 'update';
        id_pengiriman = id;
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('.modal-title').text('Edit Pengiriman'); // set a title to modal header

        // Load form edit
        $.ajax({
            url: "<?php echo site_url('pengiriman/form_input') ?>",
            type: "GET",
            dataType: "HTML",
            timeout: 10000,
            success: function(data) {
                if (data && data.trim().length > 0) {
                    $('#modal-body').html(data);

                    // Load data for edit
                    $.ajax({
                        url: "<?php echo site_url('pengiriman/edit/') ?>" + id,
                        type: "GET",
                        dataType: "JSON",
                        timeout: 10000,
                        success: function(data) {
                            if (data && data.id) {
                                $('[name="id"]').val(data.id);
                                $('[name="nomor_pengiriman"]').val(data.nomor_pengiriman);
                                $('[name="tanggal_pengiriman"]').val(data.tanggal_pengiriman);
                                $('[name="id_pesanan"]').val(data.id_pesanan).trigger('change');
                                $('[name="id_pelanggan"]').val(data.id_pelanggan).trigger('change');
                                $('[name="alamat_pengiriman"]').val(data.alamat_pengiriman);
                                $('[name="ekspedisi"]').val(data.ekspedisi);
                                $('[name="nomor_resi"]').val(data.nomor_resi);
                                $('[name="biaya_pengiriman"]').val(data.biaya_pengiriman);
                                $('[name="status"]').val(data.status).trigger('change');
                                $('[name="estimasi_tiba"]').val(data.estimasi_tiba);
                                $('[name="keterangan"]').val(data.keterangan);

                                // Disable nomor field dan hide tombol generate saat edit
                                $('[name="nomor_pengiriman"]').prop('readonly', true);
                                $('#btn-generate-nomor').hide();

                                $('#modal_form').modal('show'); // show bootstrap modal after loading form
                            } else {
                                Swal.fire({
                                    title: 'Error!',
                                    text: 'Data pengiriman tidak ditemukan atau kosong.',
                                    icon: 'error',
                                    confirmButtonText: 'OK'
                                });
                            }
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            console.log('Edit Data Error:', {
                                status: jqXHR.status,
                                responseText: jqXHR.responseText,
                                textStatus: textStatus
                            });

                            Swal.fire({
                                title: 'Error!',
                                text: 'Terjadi kesalahan saat memuat data untuk edit.',
                                icon: 'error',
                                confirmButtonText: 'OK',
                                footer: 'Status: ' + jqXHR.status
                            });
                        }
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Form edit kosong atau tidak dapat dimuat.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log('Form Load Error:', {
                    status: jqXHR.status,
                    responseText: jqXHR.responseText,
                    textStatus: textStatus
                });

                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat form edit.',
                    icon: 'error',
                    confirmButtonText: 'OK',
                    footer: 'Status: ' + jqXHR.status
                });
            }
        });
    }

    function detail(id) {
        // Ensure any existing modals are properly closed
        $('.modal').modal('hide');
        $('body').removeClass('modal-open');
        $('.modal-backdrop').remove();

        // Load detail in modal instead of redirect
        $('.modal-title').text('Detail Pengiriman');

        // Load detail content
        $.ajax({
            url: "<?php echo site_url('pengiriman/detail_modal/') ?>" + id,
            type: "GET",
            dataType: "HTML",
            timeout: 10000,
            success: function(data) {
                if (data && data.trim().length > 0) {
                    $('#modal-body-detail').html(data);

                    // Clean up any existing modal event handlers
                    $('#modal_detail').off('shown.bs.modal hidden.bs.modal');

                    // Initialize detail table after modal is shown
                    $('#modal_detail').on('shown.bs.modal', function() {
                        // Destroy existing DataTable if exists
                        if ($.fn.DataTable.isDataTable('#tbl_detail_modal')) {
                            $('#tbl_detail_modal').DataTable().destroy();
                        }
                        load_detail_table_modal(id);
                    });

                    // Clean up when modal is hidden
                    $('#modal_detail').on('hidden.bs.modal', function() {
                        // Destroy DataTable
                        if ($.fn.DataTable.isDataTable('#tbl_detail_modal')) {
                            $('#tbl_detail_modal').DataTable().destroy();
                        }
                        // Clear content
                        $('#modal-body-detail').empty();
                        // Ensure body scroll is restored
                        $('body').removeClass('modal-open').css('overflow', '');
                        $('.modal-backdrop').remove();
                    });

                    $('#modal_detail').modal('show');
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Detail pengiriman kosong atau tidak dapat dimuat.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log('Detail Load Error:', {
                    status: jqXHR.status,
                    responseText: jqXHR.responseText,
                    textStatus: textStatus
                });

                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat detail pengiriman.',
                    icon: 'error',
                    confirmButtonText: 'OK',
                    footer: 'Status: ' + jqXHR.status
                });
            }
        });
    }

    function save() {
        $('#btnSave').text('Menyimpan...'); //change button text
        $('#btnSave').attr('disabled', true); //set button disable 

        var url;
        if (save_method == 'add') {
            url = "<?php echo site_url('pengiriman/insert') ?>";
        } else {
            url = "<?php echo site_url('pengiriman/update') ?>";
        }

        // ajax adding data to database
        $.ajax({
            url: url,
            type: "POST",
            data: $('#form').serialize(),
            dataType: "JSON",
            success: function(data) {
                if (data.status) //if success close modal and reload ajax table
                {
                    $('#modal_form').modal('hide');
                    table.ajax.reload(null, false);
                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Data pengiriman berhasil disimpan.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                } else {
                    for (var i = 0; i < data.inputerror.length; i++) {
                        $('[name="' + data.inputerror[i] + '"]').parent().parent().addClass('has-error'); //select parent twice to select div form-group class and add has-error class
                        $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]); //select span help-block class set text error string
                    }
                }
                $('#btnSave').text('Simpan'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable 
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menyimpan data.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                $('#btnSave').text('Simpan'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable 
            }
        });
    }

    function hapus(id) {
        Swal.fire({
            title: 'Konfirmasi',
            text: 'Apakah Anda yakin ingin menghapus data pengiriman ini?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: "<?php echo site_url('pengiriman/hapus/') ?>" + id,
                    type: "POST",
                    dataType: "JSON",
                    success: function(data) {
                        table.ajax.reload(null, false);
                        Swal.fire({
                            title: 'Berhasil!',
                            text: 'Data pengiriman berhasil dihapus.',
                            icon: 'success',
                            confirmButtonText: 'OK'
                        });
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat menghapus data.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }

    function detail_pesanan(id_pesanan) {
        // Show loading
        /* Swal.fire({
            title: 'Memuat Detail Pesanan...',
            text: 'Sedang mengambil data pesanan, mohon tunggu.',
            icon: 'info',
            allowOutsideClick: false,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        }); */

        // Get pesanan detail via AJAX
        $.ajax({
            url: "<?php echo site_url('pengiriman/get_pesanan_info') ?>",
            type: "POST",
            data: {
                id_pesanan: id_pesanan
            },
            dataType: "JSON",
            success: function(response) {
                Swal.close();

                if (response.status) {
                    show_pesanan_detail_modal(response);
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: response.message,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.close();
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat mengambil detail pesanan.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function show_pesanan_detail_modal(data) {
        const pesanan = data.pesanan;
        const detail = data.detail;
        const shipping_history = data.shipping_history;

        // Build detail items table
        let detailTable = '';
        if (detail && detail.length > 0) {
            detailTable = `
                <table class="table table-sm table-bordered">
                    <thead class="bg-light">
                        <tr>
                            <th>No</th>
                            <th>Kode Barang</th>
                            <th>Nama Barang</th>
                            <th>Qty</th>
                            <th>Satuan</th>
                            <th>Harga Satuan</th>
                            <th>Subtotal</th>
                        </tr>
                    </thead>
                    <tbody>`;

            detail.forEach((item, index) => {
                detailTable += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${item.kode_barang}</td>
                        <td>${item.nama_barang}${item.merk ? '<br><small class="text-muted">' + item.merk + ' ' + (item.tipe || '') + '</small>' : ''}</td>
                        <td>${parseFloat(item.qty).toLocaleString()}</td>
                        <td>${item.nama_satuan || '-'}</td>
                        <td>Rp ${parseFloat(item.harga_satuan).toLocaleString()}</td>
                        <td>Rp ${parseFloat(item.subtotal).toLocaleString()}</td>
                    </tr>`;
            });

            detailTable += `</tbody></table>`;
        } else {
            detailTable = '<p class="text-muted">Tidak ada detail item.</p>';
        }

        // Build shipping history table
        let shippingTable = '';
        if (shipping_history && shipping_history.length > 0) {
            shippingTable = `
                <table class="table table-sm table-bordered">
                    <thead class="bg-light">
                        <tr>
                            <th>Nomor Pengiriman</th>
                            <th>Tanggal</th>
                            <th>Status</th>
                            <th>Ekspedisi</th>
                            <th>Total Item</th>
                            <th>Total Qty</th>
                        </tr>
                    </thead>
                    <tbody>`;

            shipping_history.forEach((ship) => {
                const statusBadge = getStatusBadge(ship.status);
                shippingTable += `
                    <tr>
                        <td>${ship.nomor_pengiriman}</td>
                        <td>${formatDate(ship.tanggal_pengiriman)}</td>
                        <td>${statusBadge}</td>
                        <td>${ship.ekspedisi || '-'}</td>
                        <td>${ship.total_item}</td>
                        <td>${parseFloat(ship.total_qty).toLocaleString()}</td>
                    </tr>`;
            });

            shippingTable += `</tbody></table>`;
        } else {
            shippingTable = '<p class="text-muted">Belum ada riwayat pengiriman.</p>';
        }

        // Show modal with pesanan detail
        Swal.fire({
            title: `Detail Pesanan: ${pesanan.nomor_pesanan}`,
            html: `
                <div class="text-left">
                    <!-- Pesanan Info -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Tanggal Pesanan:</strong><br>
                            ${formatDate(pesanan.tanggal_pesanan)}
                        </div>
                        <div class="col-md-6">
                            <strong>Status:</strong><br>
                            ${getStatusBadge(pesanan.status)}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Pelanggan:</strong><br>
                            ${pesanan.nama_pelanggan}<br>
                            <small class="text-muted">${pesanan.kode_pelanggan}</small>
                        </div>
                        <div class="col-md-6">
                            <strong>Jenis Pesanan:</strong><br>
                            ${pesanan.jenis_pesanan}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Total Item:</strong><br>
                            ${pesanan.total_item}
                        </div>
                        <div class="col-md-4">
                            <strong>Total Qty:</strong><br>
                            ${parseFloat(pesanan.total_qty).toLocaleString()}
                        </div>
                        <div class="col-md-4">
                            <strong>Total Harga:</strong><br>
                            Rp ${parseFloat(pesanan.total_harga).toLocaleString()}
                        </div>
                    </div>

                    ${pesanan.keterangan ? `
                    <div class="row mb-3">
                        <div class="col-12">
                            <strong>Keterangan:</strong><br>
                            ${pesanan.keterangan}
                        </div>
                    </div>
                    ` : ''}

                    <!-- Detail Items -->
                    <hr>
                    <h6><i class="fa fa-list"></i> Detail Item Pesanan</h6>
                    ${detailTable}

                    <!-- Shipping History -->
                    <hr>
                    <h6><i class="fa fa-shipping-fast"></i> Riwayat Pengiriman</h6>
                    ${shippingTable}
                </div>
            `,
            width: '90%',
            showConfirmButton: true,
            confirmButtonText: 'Tutup',
            confirmButtonColor: '#6c757d'
        });
    }

    function getStatusBadge(status) {
        const statusMap = {
            // Pesanan statuses - consistent with Pesanan.php controller
            'draft': { class: 'warning', text: 'Draft' },
            'diproses': { class: 'primary', text: 'Diproses' },
            'dikirim': { class: 'info', text: 'Dikirim' },
            'selesai': { class: 'success', text: 'Selesai' },
            'dibatalkan': { class: 'danger', text: 'Dibatalkan' },
            // Pengiriman statuses - consistent with existing modules
            'prepared': { class: 'info', text: 'Prepared' },
            'shipped': { class: 'primary', text: 'Shipped' },
            'in_transit': { class: 'warning', text: 'In Transit' },
            'delivered': { class: 'success', text: 'Delivered' },
            'returned': { class: 'danger', text: 'Returned' },
            'cancelled': { class: 'secondary', text: 'Cancelled' }
        };

        const statusInfo = statusMap[status] || { class: 'secondary', text: status };
        return `<span class="badge badge-${statusInfo.class}">${statusInfo.text}</span>`;
    }

    function formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('id-ID', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    }

    // ===== MODAL DETAIL FUNCTIONS =====
    var table_detail_modal;
    var save_method_detail_modal;
    var current_pengiriman_id;

    function load_detail_table_modal(id_pengiriman) {
        current_pengiriman_id = id_pengiriman;

        if ($.fn.DataTable.isDataTable('#tbl_detail_modal')) {
            $('#tbl_detail_modal').DataTable().destroy();
        }

        // Check if action column exists (only for draft status)
        var hasActionColumn = $('#tbl_detail_modal thead th').length > 7;

        var columnDefs = [];
        if (hasActionColumn) {
            columnDefs.push({
                "targets": [-1],
                "orderable": false
            });
        }

        table_detail_modal = $("#tbl_detail_modal").DataTable({
            "responsive": true,
            "autoWidth": false,
            "language": {
                "sEmptyTable": "Belum ada detail item pengiriman"
            },
            "processing": true,
            "ajax": {
                "url": "<?php echo site_url('pengiriman/ajax_list_detail/') ?>" + id_pengiriman,
                "type": "GET"
            },
            "columnDefs": columnDefs
        });
    }

    function load_available_barang_modal() {
        $.ajax({
            url: "<?php echo site_url('pengiriman/get_barang_available_for_shipping') ?>",
            type: "POST",
            data: { id_pengiriman: current_pengiriman_id },
            dataType: "JSON",
            success: function(data) {
                var options = '<option value="">-- Pilih Barang --</option>';
                $.each(data, function(index, barang) {
                    var displayText = barang.kode_barang + ' - ' + barang.nama_barang;
                    if (barang.merk) {
                        displayText += ' (' + barang.merk + ')';
                    }
                    displayText += ' [Sisa: ' + barang.qty_remaining + ' ' + barang.nama_satuan + ']';

                    options += '<option value="' + barang.id_barang + '" ' +
                               'data-satuan="' + barang.nama_satuan + '" ' +
                               'data-qty-remaining="' + barang.qty_remaining + '">' +
                               displayText + '</option>';
                });
                $('#id_barang_modal').html(options).trigger('change');
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log('Error loading barang:', textStatus);
                $('#id_barang_modal').html('<option value="">-- Error loading barang --</option>').trigger('change');
            }
        });
    }

    function load_gudang_with_stock_modal(id_barang) {
        $.ajax({
            url: "<?php echo site_url('pengiriman/get_gudang_with_stock') ?>",
            type: "POST",
            data: { id_barang: id_barang },
            dataType: "JSON",
            success: function(data) {
                var options = '<option value="">-- Pilih Gudang --</option>';
                $.each(data, function(index, gudang) {
                    options += '<option value="' + gudang.id + '" data-stok="' + gudang.stok_tersedia + '">' +
                               gudang.nama_gudang + ' (Stok: ' + gudang.stok_tersedia + ')</option>';
                });
                $('#id_gudang_modal').html(options).trigger('change');
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log('Error loading gudang:', textStatus);
                $('#id_gudang_modal').html('<option value="">-- Error loading gudang --</option>').trigger('change');
            }
        });
    }

    function add_detail_modal() {
        save_method_detail_modal = 'add';
        $('.form-group').removeClass('has-error');
        $('.help-block').empty();
        $('.modal-title').text('Tambah Detail Pengiriman');

        // Reset form
        $('#form_detail_modal')[0].reset();
        $('[name="detail_id"]').val('');
        $('#satuan_display_modal').text('-');
        $('#qty_dikirim_modal').removeAttr('max').attr('placeholder', '0');
        $('#qty_info_modal').hide();

        // Initialize Select2 for modal
        $('#id_barang_modal, #id_gudang_modal').select2({
            dropdownParent: $('#modal_form_detail_item'),
            placeholder: "-- Pilih --",
            allowClear: true
        });

        // Load available barang
        load_available_barang_modal();

        // Event handlers for modal
        $('#id_barang_modal').off('change').on('change', function() {
            var selectedOption = $(this).find('option:selected');
            var satuan = selectedOption.data('satuan');
            var qty_remaining = selectedOption.data('qty-remaining');
            var id_barang = $(this).val();

            // Update satuan display
            $('#satuan_display_modal').text(satuan || '-');

            // Show remaining qty info
            if (qty_remaining && id_barang) {
                $('#qty_dikirim_modal').attr('max', qty_remaining);
                $('#qty_dikirim_modal').attr('placeholder', 'Max: ' + qty_remaining);
                $('#qty_remaining_display_modal').text(qty_remaining + ' ' + satuan);
                $('#qty_info_modal').show();
            } else {
                $('#qty_dikirim_modal').removeAttr('max').attr('placeholder', '0');
                $('#qty_info_modal').hide();
            }

            // Load gudang dengan stok untuk barang ini
            if (id_barang) {
                load_gudang_with_stock_modal(id_barang);
            } else {
                $('#id_gudang_modal').empty().append('<option value="">-- Pilih Gudang --</option>').trigger('change');
            }
        });

        // Event handler untuk validasi qty input
        $('#qty_dikirim_modal').off('input').on('input', function() {
            var qty_input = parseFloat($(this).val()) || 0;
            var max_qty = parseFloat($(this).attr('max')) || 0;

            if (max_qty > 0 && qty_input > max_qty) {
                $(this).addClass('is-invalid');
                $(this).next('.invalid-feedback').remove();
                $(this).after('<div class="invalid-feedback">Qty tidak boleh melebihi sisa pesanan (' + max_qty + ')</div>');
            } else {
                $(this).removeClass('is-invalid');
                $(this).next('.invalid-feedback').remove();
            }
        });

        // Clean up any existing event handlers to prevent multiple bindings
        $('#modal_form_detail_item').off('hidden.bs.modal shown.bs.modal');

        // Add modal event handlers for proper cleanup
        $('#modal_form_detail_item').on('hidden.bs.modal', function() {
            // Reset form and remove validation classes
            $('#form_detail_modal')[0].reset();
            $('.form-group').removeClass('has-error');
            $('.help-block').empty();
            $('.is-invalid').removeClass('is-invalid');
            $('.invalid-feedback').remove();

            // Destroy Select2 to prevent memory leaks
            if ($('#id_barang_modal').hasClass('select2-hidden-accessible')) {
                $('#id_barang_modal').select2('destroy');
            }
            if ($('#id_gudang_modal').hasClass('select2-hidden-accessible')) {
                $('#id_gudang_modal').select2('destroy');
            }

            // Reset button state
            $('#btnSaveDetailModal').text('Simpan').attr('disabled', false);

            // Ensure body scroll is restored
            $('body').removeClass('modal-open').css('overflow', '');
            $('.modal-backdrop').remove();
        });

        $('#modal_form_detail_item').modal('show');
    }

    function save_detail_modal() {
        $('#btnSaveDetailModal').text('Menyimpan...');
        $('#btnSaveDetailModal').attr('disabled', true);

        var url;
        if (save_method_detail_modal == 'add') {
            url = "<?php echo site_url('pengiriman/insert_detail') ?>";
        } else {
            url = "<?php echo site_url('pengiriman/update_detail') ?>";
        }

        $.ajax({
            url: url,
            type: "POST",
            data: $('#form_detail_modal').serialize(),
            dataType: "JSON",
            success: function(data) {
                if (data.status) {
                    // Close modal properly
                    $('#modal_form_detail_item').modal('hide');

                    // Wait for modal to fully close before showing success message
                    $('#modal_form_detail_item').on('hidden.bs.modal', function() {
                        // Remove the event handler to prevent multiple bindings
                        $(this).off('hidden.bs.modal');

                        // Reload table
                        if (table_detail_modal) {
                            table_detail_modal.ajax.reload(null, false);
                        }

                        // Show success message
                        Swal.fire({
                            title: 'Berhasil!',
                            text: 'Detail pengiriman berhasil disimpan.',
                            icon: 'success',
                            confirmButtonText: 'OK'
                        });
                    });
                } else {
                    if (data.message) {
                        Swal.fire({
                            title: 'Error!',
                            text: data.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    } else {
                        for (var i = 0; i < data.inputerror.length; i++) {
                            $('[name="' + data.inputerror[i] + '"]').parent().parent().addClass('has-error');
                            $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]);
                        }
                    }
                }
                $('#btnSaveDetailModal').text('Simpan');
                $('#btnSaveDetailModal').attr('disabled', false);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menyimpan data.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                $('#btnSaveDetailModal').text('Simpan');
                $('#btnSaveDetailModal').attr('disabled', false);
            }
        });
    }

    function load_from_pesanan_modal() {
        Swal.fire({
            title: 'Load Item dari Pesanan',
            text: 'Fitur ini akan memuat semua item dari pesanan yang belum dikirim. Apakah Anda ingin melanjutkan?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Ya, Load!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                // Show loading
                Swal.fire({
                    title: 'Memuat Item...',
                    text: 'Sedang memuat item dari pesanan, mohon tunggu.',
                    icon: 'info',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Call backend to load items
                $.ajax({
                    url: "<?php echo site_url('pengiriman/load_items_from_pesanan') ?>",
                    type: "POST",
                    data: {
                        id_pengiriman: current_pengiriman_id
                    },
                    dataType: "JSON",
                    success: function(data) {
                        Swal.close();

                        if (data.status) {
                            // Reload detail table
                            table_detail_modal.ajax.reload(null, false);

                            Swal.fire({
                                title: 'Berhasil!',
                                text: data.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: data.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.close();
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat memuat item dari pesanan.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }

    function edit_detail_modal(id) {
        console.log('Edit detail modal called with ID:', id);
        save_method_detail_modal = 'update';
        $('.form-group').removeClass('has-error');
        $('.help-block').empty();
        $('.modal-title').text('Edit Detail Pengiriman');

        // Initialize Select2 for modal
        $('#id_barang_modal, #id_gudang_modal').select2({
            dropdownParent: $('#modal_form_detail_item'),
            placeholder: "-- Pilih --",
            allowClear: true
        });

        // Load available barang first
        load_available_barang_modal();

        // Load data for edit
        $.ajax({
            url: "<?php echo site_url('pengiriman/edit_detail/') ?>" + id,
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                $('[name="detail_id"]').val(data.id);

                // Wait for barang to load, then set value
                setTimeout(function() {
                    $('#id_barang_modal').val(data.id_barang).trigger('change');

                    // Wait for gudang to load, then set value
                    setTimeout(function() {
                        $('#id_gudang_modal').val(data.id_gudang).trigger('change');
                    }, 500);
                }, 500);

                $('#qty_dikirim_modal').val(data.qty_dikirim);
                $('#berat_satuan_modal').val(data.berat_satuan);
                $('#keterangan_detail_modal').val(data.keterangan);

                // Event handlers for modal
                $('#id_barang_modal').off('change').on('change', function() {
                    var selectedOption = $(this).find('option:selected');
                    var satuan = selectedOption.data('satuan');
                    var qty_remaining = selectedOption.data('qty-remaining');
                    var id_barang = $(this).val();

                    // Update satuan display
                    $('#satuan_display_modal').text(satuan || '-');

                    // Show remaining qty info
                    if (qty_remaining && id_barang) {
                        $('#qty_dikirim_modal').attr('max', qty_remaining);
                        $('#qty_dikirim_modal').attr('placeholder', 'Max: ' + qty_remaining);
                        $('#qty_remaining_display_modal').text(qty_remaining + ' ' + satuan);
                        $('#qty_info_modal').show();
                    } else {
                        $('#qty_dikirim_modal').removeAttr('max').attr('placeholder', '0');
                        $('#qty_info_modal').hide();
                    }

                    // Load gudang dengan stok untuk barang ini
                    if (id_barang) {
                        load_gudang_with_stock_modal(id_barang);
                    } else {
                        $('#id_gudang_modal').empty().append('<option value="">-- Pilih Gudang --</option>').trigger('change');
                    }
                });

                // Event handler untuk validasi qty input
                $('#qty_dikirim_modal').off('input').on('input', function() {
                    var qty_input = parseFloat($(this).val()) || 0;
                    var max_qty = parseFloat($(this).attr('max')) || 0;

                    if (max_qty > 0 && qty_input > max_qty) {
                        $(this).addClass('is-invalid');
                        $(this).next('.invalid-feedback').remove();
                        $(this).after('<div class="invalid-feedback">Qty tidak boleh melebihi sisa pesanan (' + max_qty + ')</div>');
                    } else {
                        $(this).removeClass('is-invalid');
                        $(this).next('.invalid-feedback').remove();
                    }
                });

                // Clean up any existing event handlers to prevent multiple bindings
                $('#modal_form_detail_item').off('hidden.bs.modal shown.bs.modal');

                // Add modal event handlers for proper cleanup
                $('#modal_form_detail_item').on('hidden.bs.modal', function() {
                    // Reset form and remove validation classes
                    $('#form_detail_modal')[0].reset();
                    $('.form-group').removeClass('has-error');
                    $('.help-block').empty();
                    $('.is-invalid').removeClass('is-invalid');
                    $('.invalid-feedback').remove();

                    // Destroy Select2 to prevent memory leaks
                    if ($('#id_barang_modal').hasClass('select2-hidden-accessible')) {
                        $('#id_barang_modal').select2('destroy');
                    }
                    if ($('#id_gudang_modal').hasClass('select2-hidden-accessible')) {
                        $('#id_gudang_modal').select2('destroy');
                    }

                    // Reset button state
                    $('#btnSaveDetailModal').text('Simpan').attr('disabled', false);

                    // Ensure body scroll is restored
                    $('body').removeClass('modal-open').css('overflow', '');
                    $('.modal-backdrop').remove();
                });

                $('#modal_form_detail_item').modal('show');
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat data detail.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function hapus_detail_modal(id) {
        console.log('Hapus detail modal called with ID:', id);
        Swal.fire({
            title: 'Konfirmasi',
            text: 'Apakah Anda yakin ingin menghapus detail ini?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: "<?php echo site_url('pengiriman/hapus_detail/') ?>" + id,
                    type: "POST",
                    dataType: "JSON",
                    success: function(data) {
                        table_detail_modal.ajax.reload(null, false);

                        Swal.fire({
                            title: 'Berhasil!',
                            text: 'Detail pengiriman berhasil dihapus.',
                            icon: 'success',
                            confirmButtonText: 'OK'
                        });
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat menghapus data.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }

    function finalize_pengiriman_modal() {
        Swal.fire({
            title: 'Finalisasi Pengiriman',
            text: 'Setelah difinalisasi, pengiriman tidak dapat diubah lagi dan stok akan dikurangi. Apakah Anda yakin?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Ya, Finalisasi!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: "<?php echo site_url('pengiriman/finalize/') ?>" + current_pengiriman_id,
                    type: "POST",
                    dataType: "JSON",
                    success: function(data) {
                        if (data.status) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: data.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then(() => {
                                $('#modal_detail').modal('hide');
                                table.ajax.reload(null, false);
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: data.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat finalisasi pengiriman.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }

    function refresh_detail_modal() {
        if (table_detail_modal) {
            table_detail_modal.ajax.reload(null, false);
        }
    }

    // Function to properly clean up modals
    function cleanup_modals() {
        // Close all modals
        $('.modal').modal('hide');

        // Remove modal-open class from body
        $('body').removeClass('modal-open');

        // Remove all modal backdrops
        $('.modal-backdrop').remove();

        // Restore body overflow
        $('body').css('overflow', '');

        // Destroy any Select2 instances
        $('.select2-hidden-accessible').each(function() {
            $(this).select2('destroy');
        });

        // Destroy DataTables
        if ($.fn.DataTable.isDataTable('#tbl_detail_modal')) {
            $('#tbl_detail_modal').DataTable().destroy();
        }
    }

    // Make functions globally available
    window.edit_detail_modal = edit_detail_modal;
    window.hapus_detail_modal = hapus_detail_modal;
    window.add_detail_modal = add_detail_modal;
    window.load_from_pesanan_modal = load_from_pesanan_modal;
    window.finalize_pengiriman_modal = finalize_pengiriman_modal;
    window.refresh_detail_modal = refresh_detail_modal;

    // Global event handler for modal cleanup
    $(document).ready(function() {
        // Ensure proper cleanup when any modal is hidden
        $(document).on('hidden.bs.modal', '.modal', function() {
            setTimeout(function() {
                if ($('.modal.show').length === 0) {
                    $('body').removeClass('modal-open').css('overflow', '');
                    $('.modal-backdrop').remove();
                }
            }, 100);
        });

        // Prevent body scroll when modal is open
        $(document).on('shown.bs.modal', '.modal', function() {
            $('body').addClass('modal-open');
        });
    });
</script>
