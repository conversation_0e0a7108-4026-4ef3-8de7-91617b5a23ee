<?php
/**
 * Test Database Connection for Pengiriman Module
 * Check if database tables exist and have data
 */

echo "<h2>Test Database Pengiriman Module</h2>";

try {
    // Database connection
    $pdo = new PDO('mysql:host=localhost;dbname=toko_elektronik', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Check if pengiriman tables exist
    $tables = ['pengiriman', 'pengiriman_detail', 'pesanan', 'pelanggan', 'barang', 'gudang'];
    
    echo "<h3>Table Existence Check:</h3>";
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✓ Table '$table' exists</p>";
            
            // Count records
            $count_stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $count_stmt->fetch(PDO::FETCH_ASSOC)['count'];
            echo "<p style='margin-left: 20px; color: blue;'>→ Records: $count</p>";
        } else {
            echo "<p style='color: red;'>✗ Table '$table' missing</p>";
        }
    }
    
    // Check pesanan data with different statuses
    echo "<h3>Pesanan Status Check:</h3>";
    $status_stmt = $pdo->query("
        SELECT status, COUNT(*) as count 
        FROM pesanan 
        GROUP BY status 
        ORDER BY count DESC
    ");
    
    $statuses = $status_stmt->fetchAll(PDO::FETCH_ASSOC);
    if (empty($statuses)) {
        echo "<p style='color: orange;'>⚠ No pesanan data found</p>";
    } else {
        foreach ($statuses as $status) {
            echo "<p>Status: <strong>{$status['status']}</strong> - Count: {$status['count']}</p>";
        }
    }
    
    // Check pelanggan data
    echo "<h3>Pelanggan Data Check:</h3>";
    $pelanggan_stmt = $pdo->query("
        SELECT COUNT(*) as total,
               SUM(CASE WHEN aktif = 1 THEN 1 ELSE 0 END) as aktif
        FROM pelanggan
    ");
    $pelanggan_data = $pelanggan_stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>Total Pelanggan: {$pelanggan_data['total']}</p>";
    echo "<p>Pelanggan Aktif: {$pelanggan_data['aktif']}</p>";
    
    // Sample pesanan data for testing
    echo "<h3>Sample Pesanan Data:</h3>";
    $sample_stmt = $pdo->query("
        SELECT p.id, p.nomor_pesanan, p.tanggal_pesanan, p.status, 
               pel.nama as nama_pelanggan
        FROM pesanan p
        LEFT JOIN pelanggan pel ON p.id_pelanggan = pel.id
        ORDER BY p.tanggal_pesanan DESC
        LIMIT 5
    ");
    
    $samples = $sample_stmt->fetchAll(PDO::FETCH_ASSOC);
    if (empty($samples)) {
        echo "<p style='color: orange;'>⚠ No sample pesanan data found</p>";
        echo "<p>Creating sample data...</p>";
        
        // Create sample data
        try {
            // Insert sample pelanggan if not exists
            $pdo->exec("
                INSERT IGNORE INTO pelanggan (id, kode, nama, alamat, no_telepon, aktif) VALUES
                (1, 'PLG001', 'PT. Teknologi Maju', 'Jl. Sudirman No. 123, Jakarta', '021-12345678', 1),
                (2, 'PLG002', 'CV. Digital Solutions', 'Jl. Gatot Subroto No. 456, Jakarta', '021-87654321', 1)
            ");
            
            // Insert sample pesanan if not exists
            $pdo->exec("
                INSERT IGNORE INTO pesanan (id, nomor_pesanan, tanggal_pesanan, id_pelanggan, total_item, total_qty, total_harga, status) VALUES
                (1, 'PES-20250108-0001', '2025-01-08', 1, 2, 3, 15000000, 'diproses'),
                (2, 'PES-20250108-0002', '2025-01-08', 2, 3, 5, 25000000, 'diproses')
            ");
            
            echo "<p style='color: green;'>✓ Sample data created</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Error creating sample data: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>Nomor Pesanan</th><th>Tanggal</th><th>Status</th><th>Pelanggan</th></tr>";
        foreach ($samples as $sample) {
            echo "<tr>";
            echo "<td>{$sample['id']}</td>";
            echo "<td>{$sample['nomor_pesanan']}</td>";
            echo "<td>{$sample['tanggal_pesanan']}</td>";
            echo "<td>{$sample['status']}</td>";
            echo "<td>{$sample['nama_pelanggan']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test pengiriman module methods simulation
    echo "<h3>Test Pengiriman Module Methods:</h3>";
    
    // Test get_pesanan_for_shipping equivalent
    $pesanan_shipping_stmt = $pdo->query("
        SELECT p.id, p.nomor_pesanan, p.tanggal_pesanan, 
               pel.nama as nama_pelanggan, pel.alamat as alamat_pelanggan, pel.no_telepon
        FROM pesanan p
        LEFT JOIN pelanggan pel ON p.id_pelanggan = pel.id
        WHERE p.status = 'diproses'
        ORDER BY p.tanggal_pesanan DESC
    ");
    
    $pesanan_shipping = $pesanan_shipping_stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<p>Pesanan available for shipping: " . count($pesanan_shipping) . "</p>";
    
    if (!empty($pesanan_shipping)) {
        echo "<ul>";
        foreach ($pesanan_shipping as $pesanan) {
            echo "<li>{$pesanan['nomor_pesanan']} - {$pesanan['nama_pelanggan']}</li>";
        }
        echo "</ul>";
    }
    
    // Test get_pelanggan_dropdown equivalent
    $pelanggan_dropdown_stmt = $pdo->query("
        SELECT id, kode, nama, alamat, no_telepon
        FROM pelanggan
        WHERE aktif = 1
        ORDER BY nama ASC
    ");
    
    $pelanggan_dropdown = $pelanggan_dropdown_stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<p>Active pelanggan for dropdown: " . count($pelanggan_dropdown) . "</p>";
    
    if (!empty($pelanggan_dropdown)) {
        echo "<ul>";
        foreach ($pelanggan_dropdown as $pelanggan) {
            echo "<li>{$pelanggan['kode']} - {$pelanggan['nama']}</li>";
        }
        echo "</ul>";
    }
    
    // Test form data simulation
    echo "<h3>Form Data Simulation:</h3>";
    echo "<p>This simulates what the form_input method should receive:</p>";
    
    echo "<pre>";
    echo "pesanan_list: " . json_encode($pesanan_shipping, JSON_PRETTY_PRINT) . "\n\n";
    echo "pelanggan_list: " . json_encode($pelanggan_dropdown, JSON_PRETTY_PRINT) . "\n\n";
    echo "nomor_pengiriman: PGR-" . date('Ymd') . "-0001\n";
    echo "</pre>";
    
    // Test pengiriman table
    echo "<h3>Pengiriman Table Test:</h3>";
    $pengiriman_stmt = $pdo->query("SELECT COUNT(*) as count FROM pengiriman");
    $pengiriman_count = $pengiriman_stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<p>Current pengiriman records: $pengiriman_count</p>";
    
    if ($pengiriman_count > 0) {
        $sample_pengiriman_stmt = $pdo->query("
            SELECT nomor_pengiriman, tanggal_pengiriman, status, 
                   total_item, total_qty
            FROM pengiriman
            ORDER BY created_at DESC
            LIMIT 3
        ");
        
        $sample_pengiriman = $sample_pengiriman_stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Nomor</th><th>Tanggal</th><th>Status</th><th>Total Item</th><th>Total Qty</th></tr>";
        foreach ($sample_pengiriman as $pengiriman) {
            echo "<tr>";
            echo "<td>{$pengiriman['nomor_pengiriman']}</td>";
            echo "<td>{$pengiriman['tanggal_pengiriman']}</td>";
            echo "<td>{$pengiriman['status']}</td>";
            echo "<td>{$pengiriman['total_item']}</td>";
            echo "<td>{$pengiriman['total_qty']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>Conclusion:</h3>";
    echo "<p style='color: green;'>✓ Database structure is ready for pengiriman module</p>";
    echo "<p>You can now test the pengiriman module at: <a href='index.php/pengiriman' target='_blank'>index.php/pengiriman</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
    echo "<p>Please check:</p>";
    echo "<ul>";
    echo "<li>MySQL server is running</li>";
    echo "<li>Database 'toko_elektronik' exists</li>";
    echo "<li>Database credentials are correct</li>";
    echo "</ul>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}
?>
