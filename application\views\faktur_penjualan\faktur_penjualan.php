<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fa fa-file-invoice text-blue"></i> Data Faktur Penjualan</h3>
                        <div class="text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary add" onclick="add()" title="Add Data"><i class="fas fa-plus"></i> Add</button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <?= $this->session->flashdata('pesan') ?>
                        <table id="tbl_faktur_penjualan" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr class="bg-info">
                                    <th>Nomor Faktur</th>
                                    <th>Tanggal</th>
                                    <th>Pelanggan</th>
                                    <th>Jenis</th>
                                    <th>Status</th>
                                    <th>Total Item</th>
                                    <th>Total Qty</th>
                                    <th>Total Nilai</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modal Form Header -->
<div class="modal fade" id="modal_form" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Faktur Penjualan</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Form akan dimuat di sini -->
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSave" onclick="save()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detail -->
<div class="modal fade" id="modal_detail" role="dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div id="modal-detail-content">
                <!-- Detail akan dimuat di sini -->
            </div>
        </div>
    </div>
</div>

<!-- Modal Form Detail Item -->
<div class="modal fade" id="modal_form_detail_item" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Detail Item Faktur</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body-detail-item">
                <!-- Form detail item akan dimuat di sini -->
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSaveDetailItem" onclick="saveDetailItem()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script>
var table;
var save_method;

$(document).ready(function() {
    // Initialize DataTable
    table = $('#tbl_faktur_penjualan').DataTable({
        "processing": true,
        "serverSide": true,
        "order": [],
        "ajax": {
            "url": "<?= site_url('FakturPenjualan/ajax_list') ?>",
            "type": "POST"
        },
        "columnDefs": [{
            "targets": [-1],
            "orderable": false,
        }],
        "language": {
            "processing": "Memproses...",
            "lengthMenu": "Tampilkan _MENU_ entri",
            "zeroRecords": "Tidak ditemukan data yang sesuai",
            "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ entri",
            "infoEmpty": "Menampilkan 0 sampai 0 dari 0 entri",
            "infoFiltered": "(disaring dari _MAX_ entri keseluruhan)",
            "search": "Cari:",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        }
    });
});

function add() {
    save_method = 'add';
    $('.form-group').removeClass('has-error');
    $('.help-block').empty();

    // Load form first, then show modal
    $.ajax({
        url: "<?= site_url('FakturPenjualan/add_form') ?>",
        type: "GET",
        dataType: "HTML",
        success: function(data) {
            $('#modal-body').html(data);
            $('#modal_form').modal('show');
            $('.modal-title').text('Tambah Faktur Penjualan');

            // Reset form after it's loaded
            setTimeout(function() {
                if ($('#form').length > 0) {
                    $('#form')[0].reset();
                }
            }, 100);
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.error('Error loading form:', textStatus, errorThrown);
            Swal.fire({
                title: 'Error!',
                text: 'Gagal memuat form: ' + textStatus,
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    });
}

function edit(id) {
    save_method = 'update';
    $('.form-group').removeClass('has-error');
    $('.help-block').empty();

    // Load form
    $.ajax({
        url: "<?= site_url('FakturPenjualan/edit_form/') ?>" + id,
        type: "GET",
        dataType: "HTML",
        success: function(data) {
            $('#modal-body').html(data);
            $('#modal_form').modal('show');
            $('.modal-title').text('Edit Faktur Penjualan');

            // Reset form after it's loaded (optional for edit)
            setTimeout(function() {
                if ($('#form').length > 0) {
                    // Don't reset for edit, form should be populated with existing data
                    console.log('Edit form loaded successfully');
                }
            }, 100);
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.error('Error loading edit form:', textStatus, errorThrown);
            Swal.fire({
                title: 'Error!',
                text: 'Gagal memuat form edit: ' + textStatus,
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    });
}

function detail(id) {
    // Show loading first
    $('#modal_detail').modal('show');
    $('#modal-detail-content').html('<div class="modal-body text-center"><i class="fa fa-spinner fa-spin"></i> Loading...</div>');

    $.ajax({
        url: "<?= site_url('FakturPenjualan/detail/') ?>" + id,
        type: "GET",
        dataType: "HTML",
        success: function(data) {
            $('#modal-detail-content').html(data);
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.error('Error loading detail:', textStatus, errorThrown);
            $('#modal-detail-content').html('<div class="modal-body text-center text-danger"><i class="fa fa-exclamation-triangle"></i> Error loading detail: ' + textStatus + '</div>');
        }
    });
}

function save() {
    // Check if form exists
    if ($('#form').length === 0) {
        Swal.fire({
            title: 'Error!',
            text: 'Form tidak ditemukan!',
            icon: 'error',
            confirmButtonText: 'OK'
        });
        return;
    }

    $('#btnSave').text('Saving...');
    $('#btnSave').attr('disabled', true);

    var url;
    if (save_method == 'add') {
        url = "<?= site_url('FakturPenjualan/insert') ?>";
    } else {
        url = "<?= site_url('FakturPenjualan/update') ?>";
    }

    console.log('Saving to URL:', url);
    console.log('Form data:', $('#form').serialize());

    $.ajax({
        url: url,
        type: "POST",
        data: $('#form').serialize(),
        dataType: "JSON",
        success: function(data) {
            console.log('Save response:', data);
            if (data.status) {
                $('#modal_form').modal('hide');
                table.ajax.reload();
                Swal.fire({
                    title: 'Berhasil!',
                    text: 'Data berhasil disimpan',
                    icon: 'success',
                    confirmButtonText: 'OK'
                });
            } else {
                if (data.inputerror && data.error_string) {
                    for (var i = 0; i < data.inputerror.length; i++) {
                        $('[name="' + data.inputerror[i] + '"]').parent().parent().addClass('has-error');
                        $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]);
                    }
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: data.message || 'Terjadi kesalahan saat menyimpan data',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            }
            $('#btnSave').text('Save');
            $('#btnSave').attr('disabled', false);
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.error('Save error:', textStatus, errorThrown);
            console.error('Response:', jqXHR.responseText);
            Swal.fire({
                title: 'Error!',
                text: 'Gagal menyimpan data: ' + textStatus,
                icon: 'error',
                confirmButtonText: 'OK'
            });
            $('#btnSave').text('Save');
            $('#btnSave').attr('disabled', false);
        }
    });
}

function hapus(id) {
    Swal.fire({
        title: 'Apakah Anda yakin?',
        text: "Data yang dihapus tidak dapat dikembalikan!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya, hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.value) {
            console.log('Deleting ID:', id);
            $.ajax({
                url: "<?= site_url('FakturPenjualan/hapus') ?>",
                type: "POST",
                dataType: "JSON",
                data: {
                    id: id
                },
                success: function(data) {
                    console.log('Delete response:', data);
                    if (data.status) {
                        table.ajax.reload();
                        Swal.fire({
                            title: 'Terhapus!',
                            text: 'Data berhasil dihapus',
                            icon: 'success',
                            confirmButtonText: 'OK'
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: data.message || 'Gagal menghapus data',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error('Delete error:', textStatus, errorThrown);
                    console.error('Response:', jqXHR.responseText);
                    Swal.fire({
                        title: 'Error!',
                        text: 'Gagal menghapus data: ' + textStatus,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}

function printFaktur(id) {
    console.log('Printing faktur ID:', id);
    var printUrl = '<?= site_url('FakturPenjualan/print_faktur/') ?>' + id;
    console.log('Print URL:', printUrl);
    window.open(printUrl, '_blank');
}

// Add debugging for button clicks
$(document).ready(function() {
    // Debug button clicks
    $(document).on('click', 'button[onclick*="add()"]', function() {
        console.log('Add button clicked');
    });

    $(document).on('click', 'button[onclick*="edit("]', function() {
        console.log('Edit button clicked');
    });

    $(document).on('click', 'button[onclick*="detail("]', function() {
        console.log('Detail button clicked');
    });

    $(document).on('click', 'button[onclick*="hapus("]', function() {
        console.log('Delete button clicked');
    });

    $(document).on('click', 'button[onclick*="printFaktur("]', function() {
        console.log('Print button clicked');
    });
});
</script>
