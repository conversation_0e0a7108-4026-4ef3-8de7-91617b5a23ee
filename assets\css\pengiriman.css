/**
 * Pengiriman Module CSS - Custom Styling
 * Toko Elektronik - Shipping Module
 */

/* =====================================================
   SHIPPING STATUS BADGES
   ===================================================== */

.badge-draft {
    background-color: #ffc107 !important;
    color: #212529 !important;
    border: 1px solid #ffca2c;
}

.badge-prepared {
    background-color: #17a2b8 !important;
    color: white !important;
    border: 1px solid #1fc7d4;
}

.badge-shipped {
    background-color: #6f42c1 !important;
    color: white !important;
    border: 1px solid #7952cc;
}

.badge-in_transit {
    background-color: #fd7e14 !important;
    color: white !important;
    border: 1px solid #fd8c3a;
}

.badge-delivered {
    background-color: #28a745 !important;
    color: white !important;
    border: 1px solid #34ce57;
}

.badge-returned {
    background-color: #dc3545 !important;
    color: white !important;
    border: 1px solid #e4606d;
}

.badge-cancelled {
    background-color: #6c757d !important;
    color: white !important;
    border: 1px solid #7a8288;
}

/* =====================================================
   FORM STYLING
   ===================================================== */

.pengiriman-form .nav-tabs {
    border-bottom: 2px solid #dee2e6;
    margin-bottom: 0;
}

.pengiriman-form .nav-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: #6c757d;
    font-weight: 500;
    padding: 12px 20px;
}

.pengiriman-form .nav-tabs .nav-link:hover {
    border-color: transparent;
    color: #007bff;
    border-bottom-color: #007bff;
}

.pengiriman-form .nav-tabs .nav-link.active {
    color: #007bff;
    background-color: transparent;
    border-color: transparent;
    border-bottom-color: #007bff;
}

.pengiriman-form .tab-content {
    border: 1px solid #dee2e6;
    border-top: none;
    background-color: #fff;
    min-height: 400px;
}

/* =====================================================
   DETAIL TABLE STYLING
   ===================================================== */

.detail-table-container {
    background-color: #fff;
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.detail-table-container .table {
    margin-bottom: 0;
}

.detail-table-container .table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.detail-table-container .table td {
    vertical-align: middle;
}

/* =====================================================
   HEADER INFO STYLING
   ===================================================== */

.pengiriman-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 0.375rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.pengiriman-header .info-table {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 0.375rem;
    padding: 1rem;
}

.pengiriman-header .info-table td {
    border: none;
    color: white;
    padding: 0.5rem 0;
}

.pengiriman-header .info-table strong {
    color: #fff;
}

/* =====================================================
   BUTTON STYLING
   ===================================================== */

.btn-pengiriman {
    border-radius: 0.375rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease-in-out;
}

.btn-pengiriman:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

.btn-finalize {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
}

.btn-finalize:hover {
    background: linear-gradient(135deg, #218838 0%, #1ea080 100%);
    color: white;
}

/* =====================================================
   MODAL STYLING
   ===================================================== */

.modal-pengiriman .modal-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-bottom: none;
}

.modal-pengiriman .modal-title {
    color: white;
    font-weight: 600;
}

.modal-pengiriman .close {
    color: white;
    opacity: 0.8;
}

.modal-pengiriman .close:hover {
    opacity: 1;
}

/* =====================================================
   STOCK INDICATOR
   ===================================================== */

.stock-indicator {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.stock-available {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.stock-low {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.stock-empty {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* =====================================================
   RESPONSIVE DESIGN
   ===================================================== */

@media (max-width: 768px) {
    .pengiriman-form .nav-tabs .nav-link {
        padding: 8px 12px;
        font-size: 0.875rem;
    }
    
    .pengiriman-header {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .pengiriman-header .info-table {
        padding: 0.75rem;
    }
    
    .btn-pengiriman {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    .pengiriman-form .nav-tabs {
        flex-wrap: wrap;
    }
    
    .pengiriman-form .nav-tabs .nav-link {
        flex: 1;
        text-align: center;
        min-width: 0;
    }
    
    .detail-table-container {
        overflow-x: auto;
    }
    
    .table-responsive-stack tr {
        display: block;
        border: 1px solid #dee2e6;
        margin-bottom: 0.5rem;
        border-radius: 0.375rem;
    }
    
    .table-responsive-stack td {
        display: block;
        text-align: right;
        border: none;
        padding: 0.5rem;
    }
    
    .table-responsive-stack td:before {
        content: attr(data-label) ": ";
        float: left;
        font-weight: 600;
        color: #495057;
    }
}

/* =====================================================
   ANIMATION EFFECTS
   ===================================================== */

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* =====================================================
   LOADING STATES
   ===================================================== */

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 0.25rem solid #f3f3f3;
    border-top: 0.25rem solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* =====================================================
   PRINT STYLES
   ===================================================== */

@media print {
    .btn, .modal, .nav-tabs {
        display: none !important;
    }
    
    .pengiriman-header {
        background: white !important;
        color: black !important;
        border: 2px solid #000 !important;
    }
    
    .badge {
        background-color: transparent !important;
        color: black !important;
        border: 1px solid #000 !important;
    }
    
    .table {
        border-collapse: collapse !important;
    }
    
    .table th,
    .table td {
        border: 1px solid #000 !important;
    }
}
