<?php
/**
 * Test Script for Pengiriman Module
 * Run this script to test basic functionality
 */

// Basic configuration
$base_url = 'http://localhost/toko_elektronik/';

echo "=== PENGIRIMAN MODULE TEST ===\n\n";

// Test 1: Check if files exist
echo "1. Checking file structure...\n";
$files_to_check = [
    'application/controllers/Pengiriman.php',
    'application/models/Mod_pengiriman.php',
    'application/views/pengiriman/pengiriman.php',
    'application/views/pengiriman/form_input.php',
    'application/views/pengiriman/detail_pengiriman.php',
    'assets/css/pengiriman.css',
    'DB/pengiriman_setup.sql'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "   ✓ $file exists\n";
    } else {
        echo "   ✗ $file missing\n";
    }
}

echo "\n2. Checking database connection...\n";

// Test database connection
try {
    $pdo = new PDO('mysql:host=localhost;dbname=toko_elektronik', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "   ✓ Database connection successful\n";
    
    // Check if tables exist
    $tables_to_check = ['pengiriman', 'pengiriman_detail'];
    foreach ($tables_to_check as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "   ✓ Table $table exists\n";
        } else {
            echo "   ✗ Table $table missing\n";
        }
    }
    
    // Check sample data
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM pengiriman");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   ✓ Sample pengiriman records: " . $result['count'] . "\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM pengiriman_detail");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   ✓ Sample pengiriman_detail records: " . $result['count'] . "\n";
    
} catch (PDOException $e) {
    echo "   ✗ Database connection failed: " . $e->getMessage() . "\n";
}

echo "\n3. Testing URL accessibility...\n";

// Test URLs (basic check)
$urls_to_test = [
    $base_url . 'pengiriman',
    $base_url . 'pengiriman/form_input',
    $base_url . 'pengiriman/generate_nomor'
];

foreach ($urls_to_test as $url) {
    $headers = @get_headers($url);
    if ($headers && strpos($headers[0], '200') !== false) {
        echo "   ✓ $url accessible\n";
    } else {
        echo "   ? $url (check manually - may require authentication)\n";
    }
}

echo "\n4. Checking CSS integration...\n";

// Check if CSS file is accessible
$css_url = $base_url . 'assets/css/pengiriman.css';
$headers = @get_headers($css_url);
if ($headers && strpos($headers[0], '200') !== false) {
    echo "   ✓ CSS file accessible\n";
} else {
    echo "   ? CSS file (check manually)\n";
}

echo "\n5. Testing model functionality...\n";

// Test model methods (if we can include CodeIgniter)
if (file_exists('index.php')) {
    echo "   ✓ CodeIgniter entry point found\n";
    echo "   → Manual testing required for model methods\n";
} else {
    echo "   ? CodeIgniter entry point not found in current directory\n";
}

echo "\n=== TEST SUMMARY ===\n";
echo "✓ = Passed\n";
echo "✗ = Failed\n";
echo "? = Requires manual verification\n";

echo "\nNext steps:\n";
echo "1. Access the application: $base_url\n";
echo "2. Login to admin panel\n";
echo "3. Navigate to Pengiriman module\n";
echo "4. Test creating a new shipment\n";
echo "5. Test adding detail items\n";
echo "6. Test stock validation\n";
echo "7. Test finalization process\n";

echo "\nFor detailed testing:\n";
echo "- Create a test pesanan first\n";
echo "- Ensure gudang and stok_barang data exists\n";
echo "- Test with different user permissions\n";
echo "- Test responsive design on mobile\n";

echo "\n=== END OF TEST ===\n";
?>
