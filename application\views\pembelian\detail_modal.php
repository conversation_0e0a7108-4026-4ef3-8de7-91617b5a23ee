<div class="modal-header">
    <h4 class="modal-title">
        <i class="fas fa-shopping-cart"></i> Detail Pembelian: <?= $pembelian->nomor_pembelian ?>
    </h4>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
        <span aria-hidden-true">&times;</span>
    </button>
</div>

<div class="modal-body">
    <!-- Info Header Pembelian -->
    <div class="row">
        <div class="col-md-6">
            <table class="table table-borderless table-sm">
                <tr>
                    <td width="40%"><strong>Nomor Pembelian</strong></td>
                    <td>: <?= $pembelian->nomor_pembelian ?></td>
                </tr>
                <tr>
                    <td><strong>Tanggal</strong></td>
                    <td>: <?= date('d/m/Y', strtotime($pembelian->tanggal_pembelian)) ?></td>
                </tr>
                <tr>
                    <td><strong>Supplier</strong></td>
                    <td>: <?= $pembelian->nama_supplier ?> (<?= $pembelian->kode_supplier ?>)</td>
                </tr>
                <tr>
                    <td><strong>Jenis Pembelian</strong></td>
                    <td>: 
                        <?php
                        switch ($pembelian->jenis_pembelian) {
                            case 'reguler':
                                echo '<span class="badge badge-primary">Reguler</span>';
                                break;
                            case 'konsinyasi':
                                echo '<span class="badge badge-info">Konsinyasi</span>';
                                break;
                            case 'kontrak':
                                echo '<span class="badge badge-warning">Kontrak</span>';
                                break;
                            default:
                                echo '<span class="badge badge-secondary">' . ucfirst($pembelian->jenis_pembelian) . '</span>';
                        }
                        ?>
                    </td>
                </tr>
                <tr>
                    <td><strong>Status</strong></td>
                    <td>: 
                        <?php
                        switch ($pembelian->status) {
                            case 'draft':
                                echo '<span class="badge badge-secondary">Draft</span>';
                                break;
                            case 'disetujui':
                                echo '<span class="badge badge-primary">Disetujui</span>';
                                break;
                            case 'dipesan':
                                echo '<span class="badge badge-warning">Dipesan</span>';
                                break;
                            case 'diterima':
                                echo '<span class="badge badge-info">Diterima</span>';
                                break;
                            case 'selesai':
                                echo '<span class="badge badge-success">Selesai</span>';
                                break;
                            case 'dibatalkan':
                                echo '<span class="badge badge-danger">Dibatalkan</span>';
                                break;
                            default:
                                echo '<span class="badge badge-secondary">' . ucfirst($pembelian->status) . '</span>';
                        }
                        ?>
                    </td>
                </tr>
            </table>
        </div>
        <div class="col-md-6">
            <table class="table table-borderless table-sm">
                <tr>
                    <td width="40%"><strong>Total Item</strong></td>
                    <td>: <?= number_format($pembelian->total_item, 0) ?> item</td>
                </tr>
                <tr>
                    <td><strong>Total Qty</strong></td>
                    <td>: <?= number_format($pembelian->total_qty, 0) ?></td>
                </tr>
                <tr>
                    <td><strong>Subtotal</strong></td>
                    <td>: Rp <?= number_format($pembelian->subtotal, 0, ',', '.') ?></td>
                </tr>
                <tr>
                    <td><strong>PPN (<?= $pembelian->ppn_persen ?>%)</strong></td>
                    <td>: Rp <?= number_format($pembelian->ppn_nominal, 0, ',', '.') ?></td>
                </tr>
                <tr>
                    <td><strong>Total Akhir</strong></td>
                    <td>: <strong>Rp <?= number_format($pembelian->total_akhir, 0, ',', '.') ?></strong></td>
                </tr>
                <tr>
                    <td><strong>Status Pembayaran</strong></td>
                    <td>: 
                        <?php
                        switch ($pembelian->status_pembayaran) {
                            case 'belum_bayar':
                                echo '<span class="badge badge-danger">Belum Bayar</span>';
                                break;
                            case 'sebagian':
                                echo '<span class="badge badge-warning">Sebagian</span>';
                                break;
                            case 'lunas':
                                echo '<span class="badge badge-success">Lunas</span>';
                                break;
                            default:
                                echo '<span class="badge badge-secondary">-</span>';
                        }
                        ?>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <!-- Tabs untuk Detail, Tracking, dan Pembayaran -->
    <ul class="nav nav-tabs" id="detailTabs" role="tablist">
        <li class="nav-item">
            <a class="nav-link active" id="items-tab" data-toggle="tab" href="#items" role="tab">
                <i class="fas fa-list"></i> Item Pembelian
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" id="tracking-tab" data-toggle="tab" href="#tracking" role="tab">
                <i class="fas fa-history"></i> Tracking Status
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" id="payment-tab" data-toggle="tab" href="#payment" role="tab">
                <i class="fas fa-money-bill"></i> Pembayaran
            </a>
        </li>
    </ul>

    <div class="tab-content" id="detailTabsContent">
        <!-- Tab Item Pembelian -->
        <div class="tab-pane fade show active" id="items" role="tabpanel">
            <div class="mt-3">
                <?php if ($pembelian->status == 'draft'): ?>
                <div class="mb-3">
                    <button type="button" class="btn btn-sm btn-primary" onclick="addDetailItem(<?= $pembelian->id ?>)">
                        <i class="fas fa-plus"></i> Tambah Item
                    </button>
                </div>
                <?php endif; ?>

                <div class="table-responsive">
                    <table class="table table-bordered table-striped table-sm">
                        <thead class="bg-light">
                            <tr>
                                <th>No</th>
                                <th>Kode Barang</th>
                                <th>Nama Barang</th>
                                <th>Gudang</th>
                                <th>Qty</th>
                                <th>Satuan</th>
                                <th>Harga Satuan</th>
                                <th>Diskon</th>
                                <th>Subtotal</th>
                                <th>PPN</th>
                                <th>Total</th>
                                <?php if ($pembelian->status == 'draft'): ?>
                                <th>Aksi</th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($pembelian_detail)): ?>
                                <?php $no = 1; foreach ($pembelian_detail as $detail): ?>
                                <tr>
                                    <td><?= $no++ ?></td>
                                    <td><?= $detail->kode_barang ?></td>
                                    <td><?= $detail->nama_barang ?></td>
                                    <td><?= $detail->nama_gudang ?></td>
                                    <td class="text-right"><?= number_format($detail->qty, 0) ?></td>
                                    <td><?= $detail->nama_satuan ?></td>
                                    <td class="text-right">Rp <?= number_format($detail->harga_satuan, 0, ',', '.') ?></td>
                                    <td class="text-right">
                                        <?php if ($detail->diskon_persen > 0): ?>
                                            <?= $detail->diskon_persen ?>%
                                        <?php elseif ($detail->diskon_nominal > 0): ?>
                                            Rp <?= number_format($detail->diskon_nominal, 0, ',', '.') ?>
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-right">Rp <?= number_format($detail->subtotal_setelah_diskon, 0, ',', '.') ?></td>
                                    <td class="text-right">Rp <?= number_format($detail->ppn_nominal, 0, ',', '.') ?></td>
                                    <td class="text-right"><strong>Rp <?= number_format($detail->total_akhir, 0, ',', '.') ?></strong></td>
                                    <?php if ($pembelian->status == 'draft'): ?>
                                    <td>
                                        <button type="button" class="btn btn-xs btn-warning" onclick="editDetailItem(<?= $detail->id ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-xs btn-danger" onclick="deleteDetailItem(<?= $detail->id ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                    <?php endif; ?>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="<?= $pembelian->status == 'draft' ? '12' : '11' ?>" class="text-center">Belum ada item pembelian</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Tab Tracking Status -->
        <div class="tab-pane fade" id="tracking" role="tabpanel">
            <div class="mt-3">
                <div class="timeline">
                    <?php if (!empty($tracking)): ?>
                        <?php foreach ($tracking as $track): ?>
                        <div class="time-label">
                            <span class="bg-info"><?= date('d/m/Y H:i', strtotime($track->tanggal_status)) ?></span>
                        </div>
                        <div>
                            <i class="fas fa-circle bg-primary"></i>
                            <div class="timeline-item">
                                <h3 class="timeline-header">
                                    Status: <strong><?= ucfirst($track->status) ?></strong>
                                </h3>
                                <div class="timeline-body">
                                    <?= $track->keterangan ?>
                                    <?php if ($track->created_by): ?>
                                        <br><small class="text-muted">oleh: <?= $track->created_by ?></small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="text-center">
                            <p>Belum ada tracking status</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Tab Pembayaran -->
        <div class="tab-pane fade" id="payment" role="tabpanel">
            <div class="mt-3">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="info-box">
                            <span class="info-box-icon bg-info"><i class="fas fa-money-bill"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Total Pembelian</span>
                                <span class="info-box-number">Rp <?= number_format($pembelian->total_akhir, 0, ',', '.') ?></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-box">
                            <span class="info-box-icon bg-success"><i class="fas fa-check"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Total Dibayar</span>
                                <span class="info-box-number">Rp <?= number_format($pembelian->total_dibayar, 0, ',', '.') ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered table-striped table-sm">
                        <thead class="bg-light">
                            <tr>
                                <th>No</th>
                                <th>Nomor Pembayaran</th>
                                <th>Tanggal</th>
                                <th>Jumlah</th>
                                <th>Metode</th>
                                <th>Referensi</th>
                                <th>Status</th>
                                <th>Keterangan</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($pembayaran)): ?>
                                <?php $no = 1; foreach ($pembayaran as $bayar): ?>
                                <tr>
                                    <td><?= $no++ ?></td>
                                    <td><?= $bayar->nomor_pembayaran ?></td>
                                    <td><?= date('d/m/Y', strtotime($bayar->tanggal_pembayaran)) ?></td>
                                    <td class="text-right">Rp <?= number_format($bayar->jumlah_bayar, 0, ',', '.') ?></td>
                                    <td><?= ucfirst($bayar->metode_pembayaran) ?></td>
                                    <td><?= $bayar->nomor_referensi ?></td>
                                    <td>
                                        <?php
                                        switch ($bayar->status) {
                                            case 'pending':
                                                echo '<span class="badge badge-warning">Pending</span>';
                                                break;
                                            case 'verified':
                                                echo '<span class="badge badge-success">Verified</span>';
                                                break;
                                            case 'rejected':
                                                echo '<span class="badge badge-danger">Rejected</span>';
                                                break;
                                        }
                                        ?>
                                    </td>
                                    <td><?= $bayar->keterangan ?></td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="8" class="text-center">Belum ada pembayaran</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <?php if ($pembelian->status == 'draft'): ?>
        <button type="button" class="btn btn-success" onclick="updateStatus(<?= $pembelian->id ?>, 'disetujui')">
            <i class="fas fa-check"></i> Setujui
        </button>
    <?php elseif ($pembelian->status == 'disetujui'): ?>
        <button type="button" class="btn btn-warning" onclick="updateStatus(<?= $pembelian->id ?>, 'dipesan')">
            <i class="fas fa-shopping-cart"></i> Pesan
        </button>
    <?php elseif ($pembelian->status == 'dipesan'): ?>
        <button type="button" class="btn btn-info" onclick="updateStatus(<?= $pembelian->id ?>, 'diterima')">
            <i class="fas fa-truck"></i> Terima
        </button>
    <?php endif; ?>
    
    <button type="button" class="btn btn-primary" onclick="printPO(<?= $pembelian->id ?>)">
        <i class="fas fa-print"></i> Print PO
    </button>
    
    <?php if ($pembelian->status_pembayaran != 'lunas'): ?>
        <button type="button" class="btn btn-warning" onclick="showPayment(<?= $pembelian->id ?>)">
            <i class="fas fa-money-bill"></i> Pembayaran
        </button>
    <?php endif; ?>
    
    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
</div>

<!-- Modal untuk Form Detail Item -->
<div class="modal fade" id="modal_detail_item" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Form Item Pembelian</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-detail-item-body">
                <!-- Form detail item akan dimuat di sini -->
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSaveDetailItem" onclick="saveDetailItem()" class="btn btn-primary">Simpan</button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
            </div>
        </div>
    </div>
</div>

<script>
var detail_save_method;
var detail_item_id;

function addDetailItem(id_pembelian) {
    detail_save_method = 'add';
    $('.modal-title').text('Tambah Item Pembelian');
    
    $.ajax({
        url: "<?php echo site_url('pembelian/form_detail_item') ?>",
        type: "GET",
        dataType: "HTML",
        success: function(data) {
            $('#modal-detail-item-body').html(data);
            $('[name="id_pembelian"]').val(id_pembelian);
            $('#modal_detail_item').modal('show');
        },
        error: function() {
            alert('Error loading form');
        }
    });
}

function editDetailItem(id) {
    detail_save_method = 'update';
    detail_item_id = id;
    $('.modal-title').text('Edit Item Pembelian');
    
    $.ajax({
        url: "<?php echo site_url('pembelian/form_detail_item') ?>",
        type: "GET",
        dataType: "HTML",
        success: function(data) {
            $('#modal-detail-item-body').html(data);
            
            // Load data untuk edit
            $.ajax({
                url: "<?php echo site_url('pembelian/get_detail_item') ?>",
                type: "POST",
                data: { id: id },
                dataType: "JSON",
                success: function(response) {
                    if (response.status == 'success') {
                        var data = response.data;
                        $('[name="id"]').val(data.id);
                        $('[name="id_pembelian"]').val(data.id_pembelian);
                        $('[name="id_barang"]').val(data.id_barang).trigger('change');
                        $('[name="id_gudang"]').val(data.id_gudang).trigger('change');
                        $('[name="qty"]').val(data.qty);
                        $('[name="harga_satuan"]').val(data.harga_satuan);
                        $('[name="diskon_persen"]').val(data.diskon_persen);
                        $('[name="diskon_nominal"]').val(data.diskon_nominal);
                        $('[name="ppn_persen"]').val(data.ppn_persen);
                        $('[name="keterangan"]').val(data.keterangan);
                        
                        $('#modal_detail_item').modal('show');
                    }
                }
            });
        },
        error: function() {
            alert('Error loading form');
        }
    });
}

function saveDetailItem() {
    $('#btnSaveDetailItem').text('Menyimpan...').attr('disabled', true);
    
    var url;
    if (detail_save_method == 'add') {
        url = "<?php echo site_url('pembelian/add_detail') ?>";
    } else {
        url = "<?php echo site_url('pembelian/update_detail') ?>";
    }
    
    var formData = new FormData($('#form_detail_item')[0]);
    $.ajax({
        url: url,
        type: "POST",
        data: formData,
        contentType: false,
        processData: false,
        dataType: "JSON",
        success: function(response) {
            if (response.status == 'success') {
                $('#modal_detail_item').modal('hide');
                toastr.success(response.message);
                // Reload detail modal
                detail(<?= $pembelian->id ?>);
                // Reload main table
                if (typeof table !== 'undefined') {
                    table.ajax.reload();
                }
            } else {
                toastr.error(response.message);
            }
            $('#btnSaveDetailItem').text('Simpan').attr('disabled', false);
        },
        error: function() {
            toastr.error('Terjadi kesalahan sistem!');
            $('#btnSaveDetailItem').text('Simpan').attr('disabled', false);
        }
    });
}

function deleteDetailItem(id) {
    if (confirm('Apakah Anda yakin ingin menghapus item ini?')) {
        $.ajax({
            url: "<?php echo site_url('pembelian/delete_detail') ?>",
            type: "POST",
            data: { id: id },
            dataType: "JSON",
            success: function(response) {
                if (response.status == 'success') {
                    toastr.success(response.message);
                    // Reload detail modal
                    detail(<?= $pembelian->id ?>);
                    // Reload main table
                    if (typeof table !== 'undefined') {
                        table.ajax.reload();
                    }
                } else {
                    toastr.error(response.message);
                }
            },
            error: function() {
                toastr.error('Terjadi kesalahan sistem!');
            }
        });
    }
}
</script>