<style>
/* Fix modal scroll issues */
.modal-open {
    overflow: hidden;
}

.modal {
    overflow-x: hidden;
    overflow-y: auto;
}

.modal-dialog {
    margin: 1.75rem auto;
}

.modal-body {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
}

/* Ensure Select2 dropdown appears above modal */
.select2-container {
    z-index: 9999 !important;
}

.select2-dropdown {
    z-index: 9999 !important;
}

/* Fix for nested modals */
.modal-backdrop.show:nth-of-type(even) {
    z-index: 1051 !important;
}

.modal:nth-of-type(even) {
    z-index: 1052 !important;
}
</style>

<!-- Header Info -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary">
                <h5 class="card-title text-white mb-0">
                    <i class="fa fa-shipping-fast"></i> <?= $pengiriman_data->nomor_pengiriman ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td width="150"><strong>Nomor Pengiriman</strong></td>
                                <td>: <?= $pengiriman_data->nomor_pengiriman ?></td>
                            </tr>
                            <tr>
                                <td><strong>Tanggal</strong></td>
                                <td>: <?= date('d/m/Y', strtotime($pengiriman_data->tanggal_pengiriman)) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Pesanan</strong></td>
                                <td>: <?= $pengiriman_data->nomor_pesanan ?></td>
                            </tr>
                            <tr>
                                <td><strong>Pelanggan</strong></td>
                                <td>: <?= $pengiriman_data->nama_pelanggan ?></td>
                            </tr>
                            <tr>
                                <td><strong>Status</strong></td>
                                <td>:
                                    <?php
                                    $status_class = '';
                                    switch($pengiriman_data->status) {
                                        case 'draft': $status_class = 'badge-secondary'; break;
                                        case 'prepared': $status_class = 'badge-warning'; break;
                                        case 'shipped': $status_class = 'badge-info'; break;
                                        case 'delivered': $status_class = 'badge-success'; break;
                                        case 'cancelled': $status_class = 'badge-danger'; break;
                                        default: $status_class = 'badge-secondary';
                                    }
                                    ?>
                                    <span class="badge <?= $status_class ?>"><?= ucfirst($pengiriman_data->status) ?></span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td width="150"><strong>Ekspedisi</strong></td>
                                <td>: <?= $pengiriman_data->ekspedisi ?: '-' ?></td>
                            </tr>
                            <tr>
                                <td><strong>Nomor Resi</strong></td>
                                <td>: <?= $pengiriman_data->nomor_resi ?: '-' ?></td>
                            </tr>
                            <tr>
                                <td><strong>Total Item</strong></td>
                                <td>: <?= number_format($pengiriman_data->total_item, 0) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Total Qty</strong></td>
                                <td>: <?= number_format($pengiriman_data->total_qty, 2) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Total Berat</strong></td>
                                <td>: <?= number_format($pengiriman_data->total_berat, 2) ?> kg</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <?php if ($pengiriman_data->alamat_pengiriman): ?>
                <div class="row mt-2">
                    <div class="col-12">
                        <strong>Alamat Pengiriman:</strong><br>
                        <?= nl2br($pengiriman_data->alamat_pengiriman) ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if ($pengiriman_data->keterangan): ?>
                <div class="row mt-2">
                    <div class="col-12">
                        <strong>Keterangan:</strong><br>
                        <?= nl2br($pengiriman_data->keterangan) ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Detail Items -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0"><i class="fa fa-list text-blue"></i> Detail Item Pengiriman</h5>
                <div class="card-tools">
                    <?php if ($pengiriman_data->status == 'draft'): ?>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="add_detail_modal()" title="Tambah Item">
                            <i class="fas fa-plus"></i> Tambah Item
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="load_from_pesanan_modal()" title="Load dari Pesanan">
                            <i class="fas fa-download"></i> Load dari Pesanan
                        </button>
                    <?php endif; ?>

                    <?php if ($pengiriman_data->status == 'prepared'): ?>
                        <button type="button" class="btn btn-sm btn-success" onclick="finalize_pengiriman_modal()" title="Finalisasi Pengiriman">
                            <i class="fas fa-check"></i> Finalisasi Pengiriman
                        </button>
                    <?php endif; ?>

                    <button type="button" class="btn btn-sm btn-outline-info" onclick="refresh_detail_modal()" title="Refresh">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                </div>
            </div>
            <div class="card-body">
                <table id="tbl_detail_modal" class="table table-bordered table-striped table-hover table-sm">
                    <thead>
                        <tr class="bg-info">
                            <th width="50">No</th>
                            <th>Barang</th>
                            <th>Gudang</th>
                            <th>Qty Dikirim</th>
                            <th>Berat Satuan</th>
                            <th>Total Berat</th>
                            <th>Keterangan</th>
                            <?php if ($pengiriman_data->status == 'draft'): ?>
                            <th width="100">Aksi</th>
                            <?php endif; ?>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal Form Detail Item -->
<div class="modal fade" id="modal_form_detail_item" role="dialog" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <h4 class="modal-title text-white">Form Detail Pengiriman</h4>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form action="#" id="form_detail_modal" class="form-horizontal">
                    <input type="hidden" value="" name="detail_id" />
                    <input type="hidden" value="<?= $id_pengiriman ?>" name="id_pengiriman" />
                    
                    <div class="form-group row">
                        <label for="id_barang_modal" class="col-sm-3 col-form-label">Barang <span class="text-danger">*</span></label>
                        <div class="col-sm-9">
                            <select class="form-control select2" name="id_barang" id="id_barang_modal" style="width: 100%;" required>
                                <option value="">-- Pilih Barang --</option>
                                <!-- Options will be loaded dynamically based on order -->
                            </select>
                            <small class="form-text text-muted">Hanya menampilkan barang dari pesanan yang belum terpenuhi.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="id_gudang_modal" class="col-sm-3 col-form-label">Gudang <span class="text-danger">*</span></label>
                        <div class="col-sm-9">
                            <select class="form-control select2" name="id_gudang" id="id_gudang_modal" style="width: 100%;" required>
                                <option value="">-- Pilih Gudang --</option>
                            </select>
                            <small class="form-text text-muted">Hanya gudang dengan stok tersedia yang ditampilkan.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="qty_dikirim_modal" class="col-sm-3 col-form-label">Qty Dikirim <span class="text-danger">*</span></label>
                        <div class="col-sm-9">
                            <div class="input-group">
                                <input type="number" class="form-control" name="qty_dikirim" id="qty_dikirim_modal" 
                                       placeholder="0" min="0.01" step="0.01" required>
                                <div class="input-group-append">
                                    <span class="input-group-text" id="satuan_display_modal">-</span>
                                </div>
                            </div>
                            <small class="form-text text-muted">Jumlah barang yang akan dikirim. Qty akan dibatasi sesuai sisa pesanan.</small>
                            <div id="qty_info_modal" class="mt-1" style="display: none;">
                                <small class="text-info">
                                    <i class="fa fa-info-circle"></i> 
                                    Sisa pesanan: <span id="qty_remaining_display_modal">-</span>
                                </small>
                            </div>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="berat_satuan_modal" class="col-sm-3 col-form-label">Berat Satuan (kg)</label>
                        <div class="col-sm-9">
                            <input type="number" class="form-control" name="berat_satuan" id="berat_satuan_modal" 
                                   placeholder="0.00" min="0" step="0.01">
                            <small class="form-text text-muted">Berat per satuan barang dalam kilogram.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="keterangan_detail_modal" class="col-sm-3 col-form-label">Keterangan</label>
                        <div class="col-sm-9">
                            <textarea class="form-control" name="keterangan_detail" id="keterangan_detail_modal" 
                                      placeholder="Keterangan tambahan (opsional)" rows="3"></textarea>
                            <span class="help-block"></span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSaveDetailModal" onclick="save_detail_modal()" class="btn btn-primary">
                    <i class="fa fa-save"></i> Simpan
                </button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fa fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>
