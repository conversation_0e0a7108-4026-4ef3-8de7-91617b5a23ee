/**
 * Status Manager JavaScript
 * Centralized status handling for all modules
 * Toko Elektronik - Consistent Status Management
 */

class StatusManager {
    constructor() {
        this.statusConfig = {
            // Warehouse Operations
            'barang_masuk': {
                'draft': { label: 'Draft', class: 'badge-warning', icon: 'fas fa-edit' },
                'final': { label: 'Final', class: 'badge-success', icon: 'fas fa-check-double' }
            },
            'barang_keluar': {
                'draft': { label: 'Draft', class: 'badge-warning', icon: 'fas fa-edit' },
                'final': { label: 'Final', class: 'badge-success', icon: 'fas fa-check-double' }
            },
            'stok_opname': {
                'draft': { label: 'Draft', class: 'badge-warning', icon: 'fas fa-edit' },
                'final': { label: 'Final', class: 'badge-success', icon: 'fas fa-check-double' }
            },
            
            // Sales & Purchase Operations
            'pesanan': {
                'draft': { label: 'Draft', class: 'badge-warning', icon: 'fas fa-edit' },
                'confirmed': { label: 'Dikonfirmasi', class: 'badge-info', icon: 'fas fa-check' },
                'shipped': { label: 'Di<PERSON>rim', class: 'badge-primary', icon: 'fas fa-shipping-fast' },
                'completed': { label: 'Selesai', class: 'badge-success', icon: 'fas fa-check-double' },
                'cancelled': { label: 'Dibatalkan', class: 'badge-danger', icon: 'fas fa-times' }
            },
            'pembelian': {
                'draft': { label: 'Draft', class: 'badge-warning', icon: 'fas fa-edit' },
                'approved': { label: 'Disetujui', class: 'badge-info', icon: 'fas fa-check' },
                'ordered': { label: 'Dipesan', class: 'badge-primary', icon: 'fas fa-shopping-cart' },
                'received': { label: 'Diterima', class: 'badge-secondary', icon: 'fas fa-box' },
                'completed': { label: 'Selesai', class: 'badge-success', icon: 'fas fa-check-double' },
                'cancelled': { label: 'Dibatalkan', class: 'badge-danger', icon: 'fas fa-times' }
            },
            
            // Shipping Operations
            'pengiriman': {
                'prepared': { label: 'Disiapkan', class: 'badge-warning', icon: 'fas fa-box-open' },
                'shipped': { label: 'Dikirim', class: 'badge-primary', icon: 'fas fa-shipping-fast' },
                'in_transit': { label: 'Dalam Perjalanan', class: 'badge-info', icon: 'fas fa-truck' },
                'delivered': { label: 'Diterima', class: 'badge-success', icon: 'fas fa-check-circle' },
                'returned': { label: 'Dikembalikan', class: 'badge-danger', icon: 'fas fa-undo' }
            },
            
            // Financial Operations
            'faktur_penjualan': {
                'draft': { label: 'Draft', class: 'badge-warning', icon: 'fas fa-edit' },
                'issued': { label: 'Diterbitkan', class: 'badge-info', icon: 'fas fa-file-invoice' },
                'paid': { label: 'Dibayar', class: 'badge-success', icon: 'fas fa-money-check-alt' },
                'overdue': { label: 'Terlambat', class: 'badge-danger', icon: 'fas fa-exclamation-triangle' },
                'cancelled': { label: 'Dibatalkan', class: 'badge-secondary', icon: 'fas fa-times' }
            },
            'pembelian_pembayaran': {
                'pending': { label: 'Menunggu', class: 'badge-warning', icon: 'fas fa-clock' },
                'verified': { label: 'Terverifikasi', class: 'badge-success', icon: 'fas fa-check-circle' },
                'rejected': { label: 'Ditolak', class: 'badge-danger', icon: 'fas fa-times-circle' }
            },
            
            // Transfer Operations
            'transfer_stok': {
                'draft': { label: 'Draft', class: 'badge-warning', icon: 'fas fa-edit' },
                'shipped': { label: 'Dikirim', class: 'badge-primary', icon: 'fas fa-shipping-fast' },
                'received': { label: 'Diterima', class: 'badge-success', icon: 'fas fa-check-circle' },
                'cancelled': { label: 'Dibatalkan', class: 'badge-danger', icon: 'fas fa-times' }
            }
        };

        this.statusTransitions = {
            'pesanan': {
                'draft': ['confirmed', 'cancelled'],
                'confirmed': ['shipped', 'cancelled'],
                'shipped': ['completed'],
                'completed': [],
                'cancelled': []
            },
            'pembelian': {
                'draft': ['approved', 'cancelled'],
                'approved': ['ordered', 'cancelled'],
                'ordered': ['received', 'cancelled'],
                'received': ['completed'],
                'completed': [],
                'cancelled': []
            },
            'pengiriman': {
                'prepared': ['shipped', 'returned'],
                'shipped': ['in_transit', 'returned'],
                'in_transit': ['delivered', 'returned'],
                'delivered': [],
                'returned': []
            },
            'faktur_penjualan': {
                'draft': ['issued', 'cancelled'],
                'issued': ['paid', 'overdue', 'cancelled'],
                'paid': [],
                'overdue': ['paid', 'cancelled'],
                'cancelled': []
            },
            'transfer_stok': {
                'draft': ['shipped', 'cancelled'],
                'shipped': ['received'],
                'received': [],
                'cancelled': []
            },
            'barang_masuk': {
                'draft': ['final'],
                'final': []
            },
            'barang_keluar': {
                'draft': ['final'],
                'final': []
            },
            'stok_opname': {
                'draft': ['final'],
                'final': []
            }
        };
    }

    /**
     * Generate status badge HTML
     */
    generateBadge(module, status) {
        if (!this.statusConfig[module] || !this.statusConfig[module][status]) {
            return `<span class="badge badge-secondary">${status}</span>`;
        }

        const config = this.statusConfig[module][status];
        return `<span class="badge ${config.class}">
                    <i class="${config.icon}"></i> ${config.label}
                </span>`;
    }

    /**
     * Get status label
     */
    getStatusLabel(module, status) {
        if (!this.statusConfig[module] || !this.statusConfig[module][status]) {
            return status;
        }
        return this.statusConfig[module][status].label;
    }

    /**
     * Get allowed status transitions
     */
    getAllowedTransitions(module, currentStatus) {
        if (!this.statusTransitions[module] || !this.statusTransitions[module][currentStatus]) {
            return [];
        }
        return this.statusTransitions[module][currentStatus];
    }

    /**
     * Check if status transition is allowed
     */
    canTransition(module, fromStatus, toStatus) {
        const allowedTransitions = this.getAllowedTransitions(module, fromStatus);
        return allowedTransitions.includes(toStatus);
    }

    /**
     * Update status with confirmation
     */
    updateStatus(module, id, newStatus, currentStatus = null, options = {}) {
        const config = this.statusConfig[module][newStatus];
        if (!config) {
            Swal.fire('Error', 'Status tidak valid!', 'error');
            return;
        }

        // Check transition if current status is provided
        if (currentStatus && !this.canTransition(module, currentStatus, newStatus)) {
            const currentLabel = this.getStatusLabel(module, currentStatus);
            const newLabel = this.getStatusLabel(module, newStatus);
            Swal.fire('Error', `Tidak dapat mengubah status dari ${currentLabel} ke ${newLabel}!`, 'error');
            return;
        }

        const statusLabel = config.label;
        const confirmText = options.confirmText || `Apakah Anda yakin ingin mengubah status menjadi ${statusLabel}?`;
        const url = options.url || `${module}/update_status`;

        Swal.fire({
            title: 'Konfirmasi',
            text: confirmText,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Ya, ubah!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                this._performStatusUpdate(url, id, newStatus, options);
            }
        });
    }

    /**
     * Perform the actual status update
     */
    _performStatusUpdate(url, id, status, options = {}) {
        const loadingText = options.loadingText || 'Mengubah status...';
        
        Swal.fire({
            title: loadingText,
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        $.ajax({
            url: url,
            type: 'POST',
            data: {
                id: id,
                status: status,
                ...options.extraData
            },
            dataType: 'json',
            success: (response) => {
                if (response.status === 'success') {
                    Swal.fire('Berhasil!', response.message, 'success');
                    
                    // Reload table if exists
                    if (typeof table !== 'undefined' && table.ajax) {
                        table.ajax.reload();
                    }
                    
                    // Call success callback if provided
                    if (options.onSuccess) {
                        options.onSuccess(response);
                    }
                } else {
                    Swal.fire('Error!', response.message || 'Gagal mengubah status!', 'error');
                }
            },
            error: (xhr, status, error) => {
                console.error('Status update error:', error);
                Swal.fire('Error!', 'Terjadi kesalahan sistem!', 'error');
                
                // Call error callback if provided
                if (options.onError) {
                    options.onError(xhr, status, error);
                }
            }
        });
    }

    /**
     * Generate status dropdown options
     */
    generateStatusOptions(module, currentStatus = null, allowedOnly = false) {
        if (!this.statusConfig[module]) {
            return '';
        }

        let options = '';
        const statuses = allowedOnly && currentStatus 
            ? this.getAllowedTransitions(module, currentStatus)
            : Object.keys(this.statusConfig[module]);

        statuses.forEach(status => {
            const config = this.statusConfig[module][status];
            const selected = currentStatus === status ? 'selected' : '';
            options += `<option value="${status}" ${selected} data-class="${config.class}" data-icon="${config.icon}">
                            ${config.label}
                        </option>`;
        });

        return options;
    }

    /**
     * Initialize status dropdowns with Select2
     */
    initializeStatusDropdown(selector, module, currentStatus = null) {
        const $dropdown = $(selector);
        
        // Populate options
        $dropdown.html(this.generateStatusOptions(module, currentStatus));
        
        // Initialize Select2 with custom template
        $dropdown.select2({
            theme: 'bootstrap4',
            templateResult: (state) => {
                if (!state.id) return state.text;
                
                const $option = $(state.element);
                const iconClass = $option.data('icon');
                const badgeClass = $option.data('class');
                
                return $(`<span><i class="${iconClass}"></i> ${state.text}</span>`);
            },
            templateSelection: (state) => {
                if (!state.id) return state.text;
                
                const $option = $(state.element);
                const iconClass = $option.data('icon');
                
                return $(`<span><i class="${iconClass}"></i> ${state.text}</span>`);
            }
        });
    }

    /**
     * Generate action buttons based on status
     */
    generateActionButtons(module, item, options = {}) {
        const currentStatus = item.status;
        const allowedTransitions = this.getAllowedTransitions(module, currentStatus);
        let buttons = '';

        // Detail button (always available)
        buttons += `<a class="btn btn-xs btn-outline-primary" href="javascript:void(0)" 
                       title="Detail" onclick="detail(${item.id})">
                       <i class="fas fa-list"></i>
                    </a> `;

        // Edit button (usually only for draft)
        if (currentStatus === 'draft' && !options.hideEdit) {
            buttons += `<a class="btn btn-xs btn-outline-info" href="javascript:void(0)" 
                           title="Edit" onclick="edit(${item.id})">
                           <i class="fas fa-edit"></i>
                        </a> `;
        }

        // Status transition buttons
        allowedTransitions.forEach(nextStatus => {
            const config = this.statusConfig[module][nextStatus];
            const buttonClass = this._getButtonClassForStatus(nextStatus);
            
            buttons += `<a class="btn btn-xs ${buttonClass}" href="javascript:void(0)" 
                           title="${config.label}" 
                           onclick="statusManager.updateStatus('${module}', ${item.id}, '${nextStatus}', '${currentStatus}')">
                           <i class="${config.icon}"></i>
                        </a> `;
        });

        // Print button (for non-draft status)
        if (currentStatus !== 'draft' && !options.hidePrint) {
            buttons += `<a class="btn btn-xs btn-outline-info" href="javascript:void(0)" 
                           title="Print" onclick="print${module.charAt(0).toUpperCase() + module.slice(1)}(${item.id})">
                           <i class="fas fa-print"></i>
                        </a> `;
        }

        // Delete button (usually only for draft)
        if (currentStatus === 'draft' && !options.hideDelete) {
            buttons += `<a class="btn btn-xs btn-outline-danger" href="javascript:void(0)" 
                           title="Delete" onclick="hapus(${item.id})">
                           <i class="fas fa-trash"></i>
                        </a>`;
        }

        return buttons;
    }

    /**
     * Get button class for status
     */
    _getButtonClassForStatus(status) {
        const classMap = {
            'confirmed': 'btn-outline-success',
            'approved': 'btn-outline-success',
            'shipped': 'btn-outline-primary',
            'completed': 'btn-outline-info',
            'final': 'btn-outline-success',
            'cancelled': 'btn-outline-danger',
            'issued': 'btn-outline-info',
            'paid': 'btn-outline-success'
        };
        
        return classMap[status] || 'btn-outline-secondary';
    }
}

// Initialize global status manager
const statusManager = new StatusManager();

// jQuery plugin for easy status badge generation
$.fn.statusBadge = function(module, status) {
    return this.html(statusManager.generateBadge(module, status));
};

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StatusManager;
}
