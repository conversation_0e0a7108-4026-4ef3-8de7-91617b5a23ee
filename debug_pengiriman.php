<?php
/**
 * Debug Pengiriman Module
 * Test individual components to identify the issue
 */

// Include CodeIgniter bootstrap
require_once 'index.php';

echo "<h2>Debug Pengiriman Module</h2>";

// Get CodeIgniter instance
$CI =& get_instance();

echo "<h3>1. Testing Database Connection</h3>";
try {
    $CI->load->database();
    echo "<p style='color: green;'>✓ Database loaded successfully</p>";
    
    // Test simple query
    $query = $CI->db->query("SELECT 1 as test");
    if ($query) {
        echo "<p style='color: green;'>✓ Database query successful</p>";
    } else {
        echo "<p style='color: red;'>✗ Database query failed</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
}

echo "<h3>2. Testing Model Loading</h3>";
try {
    $CI->load->model('Mod_pengiriman');
    echo "<p style='color: green;'>✓ Mod_pengiriman loaded successfully</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Model loading error: " . $e->getMessage() . "</p>";
}

echo "<h3>3. Testing Model Methods</h3>";
try {
    // Test generate_nomor_pengiriman
    $nomor = $CI->Mod_pengiriman->generate_nomor_pengiriman();
    echo "<p style='color: green;'>✓ generate_nomor_pengiriman: $nomor</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ generate_nomor_pengiriman error: " . $e->getMessage() . "</p>";
}

try {
    // Test get_pelanggan_dropdown
    $pelanggan = $CI->Mod_pengiriman->get_pelanggan_dropdown();
    echo "<p style='color: green;'>✓ get_pelanggan_dropdown: " . count($pelanggan) . " records</p>";
    if (!empty($pelanggan)) {
        echo "<ul>";
        foreach (array_slice($pelanggan, 0, 3) as $p) {
            echo "<li>{$p->kode} - {$p->nama}</li>";
        }
        echo "</ul>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ get_pelanggan_dropdown error: " . $e->getMessage() . "</p>";
}

try {
    // Test get_pesanan_for_shipping
    $pesanan = $CI->Mod_pengiriman->get_pesanan_for_shipping();
    echo "<p style='color: green;'>✓ get_pesanan_for_shipping: " . count($pesanan) . " records</p>";
    if (!empty($pesanan)) {
        echo "<ul>";
        foreach (array_slice($pesanan, 0, 3) as $p) {
            echo "<li>{$p->nomor_pesanan} - {$p->nama_pelanggan}</li>";
        }
        echo "</ul>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ get_pesanan_for_shipping error: " . $e->getMessage() . "</p>";
}

echo "<h3>4. Testing View Loading</h3>";
try {
    // Prepare test data
    $data = array(
        'pesanan_list' => array(),
        'pelanggan_list' => array(),
        'nomor_pengiriman' => 'TEST-001'
    );
    
    // Capture view output
    ob_start();
    $CI->load->view('pengiriman/form_input', $data);
    $view_output = ob_get_clean();
    
    if (!empty($view_output)) {
        echo "<p style='color: green;'>✓ View loaded successfully (" . strlen($view_output) . " characters)</p>";
    } else {
        echo "<p style='color: red;'>✗ View output is empty</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ View loading error: " . $e->getMessage() . "</p>";
}

echo "<h3>5. Testing Controller Method Simulation</h3>";
try {
    // Simulate form_input method
    $data = array();
    $data['pesanan_list'] = $CI->Mod_pengiriman->get_pesanan_for_shipping();
    $data['pelanggan_list'] = $CI->Mod_pengiriman->get_pelanggan_dropdown();
    $data['nomor_pengiriman'] = $CI->Mod_pengiriman->generate_nomor_pengiriman();

    // Ensure data is not null
    if (!is_array($data['pesanan_list'])) {
        $data['pesanan_list'] = array();
    }
    if (!is_array($data['pelanggan_list'])) {
        $data['pelanggan_list'] = array();
    }
    if (empty($data['nomor_pengiriman'])) {
        $data['nomor_pengiriman'] = 'PGR-' . date('Ymd') . '-0001';
    }

    echo "<p style='color: green;'>✓ Controller method simulation successful</p>";
    echo "<p>Data prepared:</p>";
    echo "<ul>";
    echo "<li>Pesanan list: " . count($data['pesanan_list']) . " items</li>";
    echo "<li>Pelanggan list: " . count($data['pelanggan_list']) . " items</li>";
    echo "<li>Nomor pengiriman: " . $data['nomor_pengiriman'] . "</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Controller simulation error: " . $e->getMessage() . "</p>";
}

echo "<h3>6. Testing Table Existence</h3>";
$tables = ['pengiriman', 'pengiriman_detail', 'pesanan', 'pelanggan'];
foreach ($tables as $table) {
    try {
        $query = $CI->db->query("SHOW TABLES LIKE '$table'");
        if ($query->num_rows() > 0) {
            echo "<p style='color: green;'>✓ Table '$table' exists</p>";
            
            // Count records
            $count_query = $CI->db->query("SELECT COUNT(*) as count FROM $table");
            $count = $count_query->row()->count;
            echo "<p style='margin-left: 20px; color: blue;'>→ Records: $count</p>";
        } else {
            echo "<p style='color: red;'>✗ Table '$table' missing</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error checking table '$table': " . $e->getMessage() . "</p>";
    }
}

echo "<h3>7. Testing Session and Authentication</h3>";
try {
    $CI->load->library('session');
    $logged_in = $CI->session->userdata('logged_in');
    $nama_user = $CI->session->userdata('nama_user');
    
    echo "<p>Logged in: " . ($logged_in ? 'Yes' : 'No') . "</p>";
    echo "<p>User name: " . ($nama_user ?: 'Not set') . "</p>";
    
    if (!$logged_in) {
        echo "<p style='color: orange;'>⚠ User not logged in - this might cause access issues</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Session error: " . $e->getMessage() . "</p>";
}

echo "<h3>8. Testing Direct Form Access</h3>";
echo "<p><a href='index.php/pengiriman/form_input' target='_blank'>Test Direct Form Access</a></p>";
echo "<p><a href='index.php/pengiriman' target='_blank'>Test Main Pengiriman Page</a></p>";

echo "<h3>Conclusion</h3>";
echo "<p>If all tests above pass, the issue might be:</p>";
echo "<ul>";
echo "<li>Authentication/session issues</li>";
echo "<li>JavaScript/AJAX request problems</li>";
echo "<li>URL routing issues</li>";
echo "<li>Server configuration problems</li>";
echo "</ul>";

echo "<p>Check browser console for JavaScript errors and network tab for failed requests.</p>";
?>
