# MODUL PENGIRIMAN - TOKO ELEKTRONIK

## Deskripsi
Modul Pengiriman adalah sistem manajemen pengiriman barang yang terintegrasi dengan modul pesanan. Modul ini mendukung pengiriman full dan partial dengan validasi stok otomatis dari gudang yang tersedia.

## Fitur Utama

### 1. Manajemen Pengiriman
- **Pengiriman Full**: Mengirim semua item dalam pesanan sekaligus
- **Pengiriman Partial**: Mengirim sebagian item dari pesanan
- **Validasi Stok**: Otomatis mengecek ketersediaan stok di gudang
- **Multi Gudang**: Mendukung pengiriman dari berbagai gudang

### 2. Tracking Pengiriman
- **Status Tracking**: Draft → Siap Kirim → Dalam Per<PERSON>lanan → Terkirim → Selesai
- **Riwayat Tracking**: Pencatatan setiap perubahan status dengan timestamp
- **Update Lokasi**: Menambahkan informasi lokasi saat dalam perjalanan
- **Nomor Resi**: Manajemen nomor resi dari ekspedisi

### 3. Integrasi Sistem
- **Auto Stock Update**: Otomatis mengurangi stok saat pengiriman dikonfirmasi
- **Barang Keluar**: Otomatis membuat transaksi barang keluar
- **Update Status Pesanan**: Otomatis mengupdate status pesanan berdasarkan pengiriman

## Struktur Database

### Tabel Utama

#### 1. pengiriman
```sql
- id (Primary Key)
- nomor_pengiriman (Unique)
- tanggal_pengiriman
- id_pesanan (Foreign Key)
- id_pelanggan (Foreign Key)
- id_gudang (Foreign Key)
- jenis_pengiriman (enum: 'full', 'partial')
- status (enum: 'draft', 'siap_kirim', 'dalam_perjalanan', 'terkirim', 'selesai', 'dibatalkan')
- ekspedisi
- nomor_resi
- biaya_pengiriman
- alamat_pengiriman
- nama_penerima
- telepon_penerima
- catatan_pengiriman
- tanggal_estimasi
- tanggal_terkirim
- total_item (Auto calculated)
- total_qty (Auto calculated)
- total_berat (Auto calculated)
- created_at, updated_at
- created_by, updated_by
```

#### 2. pengiriman_detail
```sql
- id (Primary Key)
- id_pengiriman (Foreign Key)
- id_pesanan_detail (Foreign Key)
- id_barang (Foreign Key)
- qty_dipesan
- qty_dikirim
- qty_sisa (Generated Column)
- harga_satuan
- subtotal (Generated Column)
- berat_satuan
- total_berat (Generated Column)
- keterangan
```

#### 3. pengiriman_tracking
```sql
- id (Primary Key)
- id_pengiriman (Foreign Key)
- status
- tanggal_status
- lokasi
- keterangan
- created_by
- created_at
```

### Views
- **v_pengiriman_summary**: View gabungan data pengiriman dengan informasi pelanggan dan gudang
- **v_pengiriman_detail**: View detail item pengiriman dengan informasi barang

### Triggers
- **trg_update_pengiriman_totals_***: Auto update total item, qty, dan berat
- **trg_pengiriman_status_tracking**: Auto insert tracking saat status berubah
- **trg_pengiriman_create_barang_keluar**: Auto create barang keluar saat pengiriman dikonfirmasi

## Alur Proses

### 1. Pembuatan Pengiriman
1. Pilih pesanan yang akan dikirim
2. Pilih gudang pengiriman
3. Tentukan jenis pengiriman (full/partial)
4. Pilih item dan qty yang akan dikirim
5. Validasi stok otomatis
6. Simpan sebagai draft

### 2. Konfirmasi Pengiriman
1. Review data pengiriman
2. Update status ke "Siap Kirim"
3. Print surat jalan
4. Update status ke "Dalam Perjalanan"
5. Stok otomatis berkurang
6. Barang keluar otomatis dibuat

### 3. Tracking Pengiriman
1. Input nomor resi dari ekspedisi
2. Update lokasi dan status perjalanan
3. Konfirmasi "Terkirim" saat sampai tujuan
4. Selesaikan pengiriman

## File Structure

```
application/
├── controllers/
│   └── Pengiriman.php
├── models/
│   └── Mod_pengiriman.php
├── views/
│   └── pengiriman/
│       ├── pengiriman.php (Main view)
│       ├── form_input.php (Form tambah/edit)
│       ├── detail_modal.php (Modal detail)
│       ├── tracking_modal.php (Modal tracking)
│       └── print_pengiriman.php (Print surat jalan)
assets/
├── css/
│   └── pengiriman.css (Custom styling)
DB/
├── pengiriman_tables.sql (Basic tables)
└── pengiriman_complete.sql (Complete setup)
```

## Instalasi

### 1. Database Setup
```sql
-- Jalankan file SQL untuk setup database
SOURCE pengiriman_complete.sql;
```

### 2. Menu Permission
```sql
-- Tambahkan menu pengiriman
INSERT INTO tbl_menu (nama_menu, link, icon, is_main_menu, is_aktif) 
VALUES ('Pengiriman', 'pengiriman', 'fa fa-shipping-fast', 0, 1);

-- Berikan akses ke level admin
INSERT INTO tbl_akses_menu (id_level, id_menu, view, add_level, edit_level, delete_level, print_level, export_level) 
VALUES (1, (SELECT id_menu FROM tbl_menu WHERE link = 'pengiriman'), 'Y', 'Y', 'Y', 'Y', 'Y', 'Y');
```

### 3. File Upload
- Upload semua file ke direktori yang sesuai
- Pastikan CSS dan JavaScript ter-load dengan benar

## Penggunaan

### 1. Membuat Pengiriman Baru
1. Klik tombol "Tambah Pengiriman"
2. Pilih pesanan dari dropdown
3. Pilih gudang pengiriman
4. Tentukan jenis pengiriman
5. Atur qty untuk setiap item
6. Isi informasi pengiriman
7. Simpan

### 2. Mengelola Status
- **Draft**: Pengiriman baru dibuat, masih bisa diedit
- **Siap Kirim**: Pengiriman siap untuk dikirim
- **Dalam Perjalanan**: Barang sedang dalam perjalanan, stok sudah berkurang
- **Terkirim**: Barang sudah sampai di tujuan
- **Selesai**: Pengiriman selesai, pesanan bisa diselesaikan
- **Dibatalkan**: Pengiriman dibatalkan

### 3. Tracking
- Klik tombol "Tracking" untuk melihat riwayat
- Tambahkan update lokasi saat dalam perjalanan
- Update nomor resi dari ekspedisi

### 4. Print Surat Jalan
- Klik tombol "Print" untuk mencetak surat jalan
- Surat jalan berisi detail barang dan informasi pengiriman
- Dilengkapi dengan area tanda tangan

## Validasi & Keamanan

### 1. Validasi Stok
- Otomatis mengecek stok sebelum menyimpan
- Mencegah pengiriman melebihi stok tersedia
- Validasi ulang saat konfirmasi pengiriman

### 2. Validasi Status
- Hanya transisi status yang valid yang diizinkan
- Pengiriman yang sudah diproses tidak bisa dihapus
- Validasi permission berdasarkan level user

### 3. Data Integrity
- Foreign key constraints
- Generated columns untuk kalkulasi otomatis
- Triggers untuk konsistensi data

## Kustomisasi

### 1. Status Tambahan
Untuk menambah status baru:
1. Update enum di tabel pengiriman
2. Tambahkan di valid_transitions array
3. Update badge styling di CSS
4. Tambahkan action button jika perlu

### 2. Field Tambahan
Untuk menambah field baru:
1. ALTER TABLE pengiriman ADD COLUMN
2. Update form_input.php
3. Update controller validation
4. Update model save/update methods

### 3. Ekspedisi Integration
Untuk integrasi dengan API ekspedisi:
1. Buat library ekspedisi baru
2. Tambahkan method tracking otomatis
3. Update controller untuk call API
4. Tambahkan cron job untuk update status

## Troubleshooting

### 1. Stok Tidak Berkurang
- Cek trigger trg_pengiriman_create_barang_keluar
- Pastikan tabel stok_barang ada dan terisi
- Cek permission database untuk trigger

### 2. Total Tidak Update
- Cek trigger trg_update_pengiriman_totals_*
- Pastikan generated columns berfungsi
- Refresh data dengan manual update

### 3. Filter Tidak Berfungsi
- Cek JavaScript console untuk error
- Pastikan DataTables ter-load dengan benar
- Cek parameter POST di controller

## Maintenance

### 1. Backup Data
```sql
-- Backup tabel pengiriman
mysqldump -u user -p database pengiriman pengiriman_detail pengiriman_tracking > backup_pengiriman.sql
```

### 2. Cleanup Data Lama
```sql
-- Hapus tracking lama (opsional)
DELETE FROM pengiriman_tracking 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 YEAR);
```

### 3. Reindex Tables
```sql
-- Optimize tables
OPTIMIZE TABLE pengiriman, pengiriman_detail, pengiriman_tracking;
```

## Support

Untuk pertanyaan atau masalah terkait modul pengiriman:
1. Cek dokumentasi ini terlebih dahulu
2. Review kode di file yang relevan
3. Cek log error di server
4. Test dengan data sample

---

**Versi**: 1.0  
**Tanggal**: Januari 2025  
**Kompatibilitas**: CodeIgniter 3.x, PHP 7.4+, MySQL 5.7+