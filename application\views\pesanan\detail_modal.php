<div class="modal-header">
    <h4 class="modal-title"><i class="fas fa-list"></i> Detail Pesanan - <?= $pesanan->nomor_pesanan ?></h4>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<div class="modal-body" style="max-height: 80vh; overflow-y: auto;">
<!-- Header Info -->
<div class="row mb-3">
    <div class="col-md-6">
        <table class="table table-sm table-borderless">
            <tr>
                <td width="140"><strong>Nomor <PERSON><PERSON></strong></td>
                <td width="10">:</td>
                <td><?= $pesanan->nomor_pesanan ?></td>
            </tr>
            <tr>
                <td><strong>Tanggal</strong></td>
                <td>:</td>
                <td><?= date('d/m/Y', strtotime($pesanan->tanggal_pesanan)) ?></td>
            </tr>
            <tr>
                <td><strong>Jenis</strong></td>
                <td>:</td>
                <td>
                    <?php if ($pesanan->jenis_pesanan == 'android'): ?>
                        <span class="badge badge-info">ANDROID</span>
                    <?php else: ?>
                        <span class="badge badge-secondary">MANUAL</span>
                    <?php endif; ?>
                </td>
            </tr>
            <tr>
                <td><strong>Pelanggan</strong></td>
                <td>:</td>
                <td><?= $pesanan->nama_pelanggan ? $pesanan->nama_pelanggan . ' (' . $pesanan->kode_pelanggan . ')' : '-' ?></td>
            </tr>
        </table>
    </div>
    <div class="col-md-6">
        <table class="table table-sm table-borderless">
            <tr>
                <td width="120"><strong>Status</strong></td>
                <td width="10">:</td>
                <td>
                    <?php
                    switch ($pesanan->status) {
                        case 'draft':
                            echo '<span class="badge badge-warning">DRAFT</span>';
                            break;
                        case 'diproses':
                            echo '<span class="badge badge-primary">DIPROSES</span>';
                            break;
                        case 'dikirim':
                            echo '<span class="badge badge-info">DIKIRIM</span>';
                            break;
                        case 'selesai':
                            echo '<span class="badge badge-success">SELESAI</span>';
                            break;
                        case 'dibatalkan':
                            echo '<span class="badge badge-danger">DIBATALKAN</span>';
                            break;
                        default:
                            echo '<span class="badge badge-secondary">' . strtoupper($pesanan->status) . '</span>';
                    }
                    ?>
                </td>
            </tr>
            <tr>
                <td><strong>Total Item</strong></td>
                <td>:</td>
                <td><?= number_format($pesanan->total_item, 0) ?> item</td>
            </tr>
            <tr>
                <td><strong>Total Qty</strong></td>
                <td>:</td>
                <td><?= number_format($pesanan->total_qty, 0) ?></td>
            </tr>
            <tr>
                <td><strong>Total Harga</strong></td>
                <td>:</td>
                <td><strong>Rp <?= number_format($pesanan->total_harga, 0, ',', '.') ?></strong></td>
            </tr>
        </table>
    </div>
</div>

<?php if (!empty($pesanan->keterangan)): ?>
<div class="row mb-3">
    <div class="col-12">
        <div class="alert alert-info">
            <strong>Keterangan:</strong> <?= $pesanan->keterangan ?>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Action Buttons -->
<div class="row mb-3">
    <div class="col-12">
        <?php if ($pesanan->status == 'draft'): ?>
        <button type="button" class="btn btn-sm btn-outline-primary" onclick="addDetailItem()">
            <i class="fas fa-plus"></i> Tambah Item
        </button>
        <button type="button" class="btn btn-sm btn-outline-warning" onclick="editPesanan(<?= $pesanan->id ?>)">
            <i class="fas fa-edit"></i> Edit Pesanan
        </button>
        <button type="button" class="btn btn-sm btn-outline-success" onclick="updateStatus('diproses')">
            <i class="fas fa-check"></i> Proses Pesanan
        </button>
        <?php elseif ($pesanan->status == 'diproses'): ?>
        <button type="button" class="btn btn-sm btn-outline-info" onclick="updateStatus('dikirim')">
            <i class="fas fa-truck"></i> Kirim Pesanan
        </button>
        <button type="button" class="btn btn-sm btn-outline-danger" onclick="updateStatus('dibatalkan')">
            <i class="fas fa-times"></i> Batalkan
        </button>
        <?php elseif ($pesanan->status == 'dikirim'): ?>
        <button type="button" class="btn btn-sm btn-outline-success" onclick="updateStatus('selesai')">
            <i class="fas fa-check-circle"></i> Selesaikan
        </button>
        <?php endif; ?>

        <!-- Print Button -->
        <button type="button" class="btn btn-sm btn-outline-dark" onclick="printPesanan()">
            <i class="fas fa-print"></i> Print Pesanan
        </button>
    </div>
</div>

<!-- Detail Items -->
<div class="row">
    <div class="col-12">
        <h6><i class="fas fa-list"></i> Detail Item Pesanan</h6>
        <div class="table-responsive">
            <table class="table table-bordered table-striped table-sm">
                <thead class="bg-light">
                    <tr>
                        <th width="5%">No</th>
                        <th width="15%">Kode Barang</th>
                        <th width="35%">Nama Barang</th>
                        <th width="10%">Qty</th>
                        <th width="10%">Satuan</th>
                        <th width="12%">Harga Satuan</th>
                        <th width="12%">Subtotal</th>
                        <?php if ($pesanan->status == 'draft'): ?>
                        <th width="10%">Aksi</th>
                        <?php endif; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($pesanan_detail)): ?>
                        <?php $no = 1; $total_nilai = 0; foreach ($pesanan_detail as $d): ?>
                        <tr>
                            <td><?= $no++ ?></td>
                            <td><?= $d->kode_barang ?></td>
                            <td>
                                <?= $d->nama_barang ?>
                                <?php if ($d->merk || $d->tipe): ?>
                                    <br><small class="text-muted"><?= trim($d->merk . ' ' . $d->tipe) ?></small>
                                <?php endif; ?>
                                <?php if ($d->keterangan): ?>
                                    <br><small class="text-info">Ket: <?= $d->keterangan ?></small>
                                <?php endif; ?>
                            </td>
                            <td class="text-right"><?= number_format($d->qty, 0) ?></td>
                            <td><?= $d->nama_satuan ?: '-' ?></td>
                            <td class="text-right">Rp <?= number_format($d->harga_satuan, 0, ',', '.') ?></td>
                            <td class="text-right">Rp <?= number_format($d->subtotal, 0, ',', '.') ?></td>
                            <?php if ($pesanan->status == 'draft'): ?>
                            <td class="text-center">
                                <button type="button" class="btn btn-xs btn-outline-info" onclick="editDetailItem(<?= $d->id ?>)" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-xs btn-outline-danger" onclick="deleteDetailItem(<?= $d->id ?>)" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                            <?php endif; ?>
                        </tr>
                        <?php $total_nilai += $d->subtotal; ?>
                        <?php endforeach; ?>

                        <!-- Total Row -->
                        <tr class="bg-light font-weight-bold">
                            <td colspan="<?= $pesanan->status == 'draft' ? '7' : '6' ?>" class="text-center">TOTAL</td>
                            <td class="text-right">Rp <?= number_format($total_nilai, 0, ',', '.') ?></td>
                            <?php if ($pesanan->status == 'draft'): ?>
                            <td></td>
                            <?php endif; ?>
                        </tr>
                    <?php else: ?>
                        <tr>
                            <td colspan="<?= $pesanan->status == 'draft' ? '8' : '7' ?>" class="text-center text-muted">
                                <i class="fas fa-info-circle"></i> Belum ada detail item pesanan
                                <?php if ($pesanan->status == 'draft'): ?>
                                    <br><a href="javascript:void(0)" onclick="addDetailItem()">Klik di sini untuk menambah item</a>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Info Tambahan -->
<div class="row mt-3">
    <div class="col-md-6">
        <h6><i class="fas fa-user"></i> Informasi Pembuatan</h6>
        <table class="table table-sm table-borderless">
            <tr>
                <td width="100"><strong>Dibuat oleh</strong></td>
                <td width="10">:</td>
                <td><?= $pesanan->created_by ?: '-' ?></td>
            </tr>
            <tr>
                <td><strong>Tanggal dibuat</strong></td>
                <td>:</td>
                <td><?= $pesanan->created_at ? date('d/m/Y H:i:s', strtotime($pesanan->created_at)) : '-' ?></td>
            </tr>
        </table>
    </div>
    <div class="col-md-6">
        <h6><i class="fas fa-clock"></i> Update Terakhir</h6>
        <table class="table table-sm table-borderless">
            <tr>
                <td width="100"><strong>Diupdate oleh</strong></td>
                <td width="10">:</td>
                <td><?= $pesanan->updated_by ?: '-' ?></td>
            </tr>
            <tr>
                <td><strong>Tanggal update</strong></td>
                <td>:</td>
                <td><?= $pesanan->updated_at ? date('d/m/Y H:i:s', strtotime($pesanan->updated_at)) : '-' ?></td>
            </tr>
        </table>
    </div>
</div>
</div>

<!-- Modal Form Detail Item -->
<div class="modal fade" id="modal_form_detail_item" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Detail Item Pesanan</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="form_detail_item">
                    <input type="hidden" name="id" id="detail_item_id">
                    <input type="hidden" name="id_pesanan" value="<?= $pesanan->id ?>">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group" id="barang_group">
                                <label for="id_barang">Barang <span class="text-danger">*</span></label>
                                <select class="form-control select2" name="id_barang" id="id_barang" required style="width: 100%;">
                                    <option value="">-- Pilih Barang --</option>
                                    <?php foreach ($barang_list as $barang): ?>
                                        <option value="<?= $barang->id ?>" data-harga="<?= $barang->harga_jual ?>"><?= $barang->kode_barang ?> - <?= $barang->nama_barang ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <span class="help-block"></span>
                            </div>

                            <div class="form-group">
                                <label for="qty">Qty <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" name="qty" id="qty" required min="0" step="1" onchange="updateTotalHarga()">
                                <span class="help-block"></span>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="harga_satuan">Harga Satuan</label>
                                <input type="number" class="form-control" name="harga_satuan" id="harga_satuan" min="0" step="0.01" onchange="updateTotalHarga()">
                                <span class="help-block"></span>
                            </div>

                            <div class="form-group">
                                <label>Subtotal</label>
                                <div class="form-control-plaintext">
                                    <strong id="subtotal_display">0</strong>
                                    <small class="text-muted ml-2">(Qty × Harga Satuan)</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="keterangan_detail_item">Keterangan</label>
                        <textarea class="form-control" name="keterangan" id="keterangan_detail_item" rows="2" placeholder="Keterangan untuk item ini (opsional)"></textarea>
                        <span class="help-block"></span>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSaveDetailItem" onclick="saveDetailItem()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <button type="button" class="btn btn-sm btn-outline-secondary" data-dismiss="modal">
        <i class="fas fa-times"></i> Tutup
    </button>
</div>

<script>
var save_method_detail_item;
var current_pesanan_id = <?= $pesanan->id ?>;

$(document).ready(function() {
    // Initialize Select2 untuk modal detail item
    $('#modal_form_detail_item').on('shown.bs.modal', function() {
        $('.select2').select2({
            dropdownParent: $('#modal_form_detail_item'),
            placeholder: "-- Pilih --",
            allowClear: true
        });
    });

    // Event handler untuk perubahan barang
    $('#id_barang').on('change', function() {
        var selectedOption = $(this).find('option:selected');
        var hargaJual = selectedOption.data('harga');

        if (hargaJual) {
            $('#harga_satuan').val(hargaJual);
            updateTotalHarga();
        }
    });
});

function addDetailItem() {
    save_method_detail_item = 'add';
    $('#form_detail_item')[0].reset();
    $('#detail_item_id').val('');
    $('#barang_group').show();
    $('#subtotal_display').text('0');
    $('.modal-title').text('Tambah Detail Item');
    $('#modal_form_detail_item').modal('show');
}

function editDetailItem(id) {
    save_method_detail_item = 'update';
    $('#barang_group').show();
    $('.modal-title').text('Edit Detail Item');

    $.ajax({
    url: "<?php echo site_url('pesanan/get_detail_item') ?>",
    type: "POST",
    data: { id: id },
    dataType: "JSON",
    success: function(data) {
    if (data.status == 'success') {
    $('#detail_item_id').val(data.data.id);
    $('#id_barang').val(data.data.id_barang).trigger('change');
    $('#qty').val(data.data.qty);
    $('#harga_satuan').val(data.data.harga_satuan);
    $('#keterangan_detail_item').val(data.data.keterangan);
    updateTotalHarga();
    $('#modal_form_detail_item').modal('show');
    } else {
    Swal.fire({
    title: 'Error!',
    text: 'Terjadi kesalahan saat memuat data detail.',
    icon: 'error',
    confirmButtonText: 'OK'
    });
    }
    },
        error: function(jqXHR, textStatus, errorThrown) {
            Swal.fire({
                title: 'Error!',
                text: 'Terjadi kesalahan saat memuat data detail.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    });
}

function saveDetailItem() {
    $('#btnSaveDetailItem').text('saving...').prop('disabled', true);

    var url;
    if (save_method_detail_item == 'add') {
        url = "<?php echo site_url('pesanan/add_detail') ?>";
    } else {
        url = "<?php echo site_url('pesanan/update_detail') ?>";
    }

    $.ajax({
        url: url,
        type: "POST",
        data: $('#form_detail_item').serialize(),
        dataType: "JSON",
        success: function(data) {
            if (data.status == 'success') {
                $('#modal_form_detail_item').modal('hide');
                Swal.fire({
                    title: 'Detail item berhasil disimpan!',
                    text: data.message,
                    icon: 'success',
                    confirmButtonText: 'OK'
                }).then(() => {
                    // Reload modal detail
                    detail(current_pesanan_id);
                    // Reload main table
                    if (typeof table !== 'undefined') {
                        table.ajax.reload();
                    }
                });
            } else {
                Swal.fire({
                    title: 'Gagal Menyimpan!',
                    text: data.message,
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            Swal.fire({
                title: 'Error!',
                text: 'Terjadi kesalahan saat menyimpan detail.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        },
        complete: function() {
            $('#btnSaveDetailItem').text('Save').prop('disabled', false);
        }
    });
}

function deleteDetailItem(id) {
    Swal.fire({
        title: 'Apakah Anda yakin?',
        text: "Detail item akan dihapus!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya, hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: "<?php echo site_url('pesanan/delete_detail') ?>",
                type: "POST",
                dataType: "JSON",
                data: {
                    id: id,
                    id_pesanan: current_pesanan_id
                },
                success: function(data) {
                    if (data.status == 'success') {
                        Swal.fire({
                            title: 'Terhapus!',
                            text: data.message,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then(() => {
                            // Reload modal detail
                            detail(current_pesanan_id);
                            // Reload main table
                            if (typeof table !== 'undefined') {
                                table.ajax.reload();
                            }
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: data.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat menghapus detail.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}

function updateStatus(status) {
    var statusText = '';
    switch(status) {
        case 'diproses': statusText = 'diproses'; break;
        case 'dikirim': statusText = 'dikirim'; break;
        case 'selesai': statusText = 'diselesaikan'; break;
        case 'dibatalkan': statusText = 'dibatalkan'; break;
    }

    Swal.fire({
        title: 'Konfirmasi',
        text: 'Apakah Anda yakin ingin mengubah status pesanan menjadi ' + statusText + '?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya, ubah!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: '<?= site_url('pesanan/update_status') ?>',
                type: 'POST',
                data: {
                    id: current_pesanan_id,
                    status: status
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status == 'success') {
                        Swal.fire({
                            title: 'Status pesanan berhasil diubah!',
                            text: response.message,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then(() => {
                            // Reload modal detail
                            detail(current_pesanan_id);
                            // Reload main table
                            if (typeof table !== 'undefined') {
                                table.ajax.reload();
                            }
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: response.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan sistem!',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}

function editPesanan(id) {
    // Close detail modal first
    $('#modal_detail').modal('hide');

    // Wait for modal to close then open edit modal
    setTimeout(function() {
        edit(id);
    }, 500);
}

function printPesanan() {
    window.open('<?= site_url('pesanan/print_pesanan/' . $pesanan->id) ?>', '_blank');
}

function updateTotalHarga() {
    var qty = parseFloat($('#qty').val()) || 0;
    var harga = parseFloat($('#harga_satuan').val()) || 0;
    var total = qty * harga;

    $('#subtotal_display').text(total.toLocaleString('id-ID'));
}
</script>