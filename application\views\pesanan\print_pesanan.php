<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Print Pesanan - <?= $pesanan->nomor_pesanan ?></title>
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="stylesheet" href="<?= base_url() ?>assets/bootstrap/css/bootstrap.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #000;
            padding-bottom: 20px;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .company-info {
            font-size: 12px;
            color: #666;
        }

        .document-title {
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0;
            text-align: center;
            text-transform: uppercase;
        }

        .info-section {
            margin-bottom: 20px;
        }

        .info-table {
            width: 100%;
            margin-bottom: 20px;
        }

        .info-table td {
            padding: 3px 0;
            vertical-align: top;
        }

        .info-table .label {
            width: 120px;
            font-weight: bold;
        }

        .info-table .separator {
            width: 10px;
            text-align: center;
        }

        .detail-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .detail-table th,
        .detail-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
        }

        .detail-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: center;
        }

        .detail-table .text-center {
            text-align: center;
        }

        .detail-table .text-right {
            text-align: right;
        }

        .total-row {
            background-color: #f9f9f9;
            font-weight: bold;
        }

        .footer {
            margin-top: 40px;
            page-break-inside: avoid;
        }

        .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 50px;
        }

        .signature-box {
            text-align: center;
            width: 200px;
        }

        .signature-line {
            border-top: 1px solid #000;
            margin-top: 60px;
            padding-top: 5px;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-draft {
            background-color: #ffc107;
            color: #000;
        }

        .status-diproses {
            background-color: #007bff;
            color: #fff;
        }

        .status-dikirim {
            background-color: #17a2b8;
            color: #fff;
        }

        .status-selesai {
            background-color: #28a745;
            color: #fff;
        }

        .status-dibatalkan {
            background-color: #dc3545;
            color: #fff;
        }

        @media print {
            body {
                margin: 0;
                padding: 15px;
            }

            .no-print {
                display: none !important;
            }

            .page-break {
                page-break-before: always;
            }
        }
    </style>
</head>

<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name">TOKO ELEKTRONIK</div>
        <div class="company-info">
            Jl. Contoh No. 123, Kota Contoh<br>
            Telp: (021) 1234567 | Email: <EMAIL>
        </div>
    </div>

    <!-- Document Title -->
    <div class="document-title">PESANAN PELANGGAN</div>

    <!-- Pesanan Info -->
    <div class="info-section">
        <table class="info-table">
            <tr>
                <td class="label">Nomor Pesanan</td>
                <td class="separator">:</td>
                <td><strong><?= $pesanan->nomor_pesanan ?></strong></td>
                <td style="width: 50px;"></td>
                <td class="label">Status</td>
                <td class="separator">:</td>
                <td>
                    <?php
                    $status_class = 'status-' . $pesanan->status;
                    $status_text = strtoupper($pesanan->status);
                    ?>
                    <span class="status-badge <?= $status_class ?>"><?= $status_text ?></span>
                </td>
            </tr>
            <tr>
                <td class="label">Tanggal Pesanan</td>
                <td class="separator">:</td>
                <td><?= date('d/m/Y', strtotime($pesanan->tanggal_pesanan)) ?></td>
                <td></td>
                <td class="label">Jenis Pesanan</td>
                <td class="separator">:</td>
                <td><?= ucfirst($pesanan->jenis_pesanan) ?></td>
            </tr>
            <tr>
                <td class="label">Pelanggan</td>
                <td class="separator">:</td>
                <td><strong><?= $pesanan->nama_pelanggan ?></strong> (<?= $pesanan->kode_pelanggan ?>)</td>
                <td></td>
                <td class="label">Total Item</td>
                <td class="separator">:</td>
                <td><?= number_format($pesanan->total_item, 0) ?> item</td>
            </tr>
            <tr>
                <td class="label">Alamat</td>
                <td class="separator">:</td>
                <td><?= $pesanan->alamat_pelanggan ?: '-' ?></td>
                <td></td>
                <td class="label">Total Qty</td>
                <td class="separator">:</td>
                <td><?= number_format($pesanan->total_qty, 0) ?></td>
            </tr>
            <?php if (!empty($pesanan->keterangan)): ?>
                <tr>
                    <td class="label">Keterangan</td>
                    <td class="separator">:</td>
                    <td colspan="5"><?= $pesanan->keterangan ?></td>
                </tr>
            <?php endif; ?>
        </table>
    </div>

    <!-- Detail Items -->
    <table class="detail-table">
        <thead<tr>
                <th width="5%">No</th>
                <th width="15%">Kode Barang</th>
                <th width="40%">Nama Barang</th>
                <th width="10%">Qty</th>
                <th width="10%">Satuan</th>
                <th width="12%">Harga Satuan</th>
                <th width="12%">Subtotal</th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($detail)): ?>
                <?php $no = 1;
                $total_nilai = 0;
                foreach ($detail as $d): ?<tr>
                        <td class="text-center"><?= $no++ ?></td>
                        <td><?= $d->kode_barang ?></td>
                        <td>
                            <?= $d->nama_barang ?>
                            <?php if ($d->merk || $d->tipe): ?>
                                <br><small style="color: #666;"><?= trim($d->merk . ' ' . $d->tipe) ?></small>
                            <?php endif; ?>
                            <?php if ($d->keterangan): ?>
                                <br><small style="color: #999; font-style: italic;">Ket: <?= $d->keterangan ?></small>
                            <?php endif; ?>
                        </td>
                        <td class="text-right"><?= number_format($d->qty, 0) ?></td>
                        <td class="text-center"><?= $d->nama_satuan ?: '-' ?></td>
                        <td class="text-right"><?= number_format($d->harga_satuan, 0) ?></td>
                        <td class="text-right"><?= number_format($d->subtotal, 0) ?></td>
                    </tr>
                    <?php $total_nilai += $d->subtotal; ?>
                <?php endforeach; ?>

                <!-- Total Row -->
                <tr class="total-row">
                    <td colspan="6" class="text-center"><strong>TOTAL</strong></td>
                    <td class="text-right"><strong><?= number_format($total_nilai, 0) ?></strong></td>
                </tr>
            <?php else: ?>
                <tr>
                    <td colspan="7" class="text-center" style="padding: 20px; color: #999;">
                        Belum ada detail item pesanan
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>

    <!-- Footer -->
    <div class="footer">
        <div style="margin-top: 30px; font-size: 11px; color: #666;">
            <strong>Catatan:</strong><br>
            - Pesanan ini berlaku sesuai dengan syarat dan ketentuan yang berlaku<br>
            - Harap simpan dokumen ini sebagai bukti pesanan<br>
            - Untuk informasi lebih lanjut, hubungi customer service kami
        </div>

        <!-- Signature Section -->
        <div class="signature-section">
            <div class="signature-box">
                <div>Pelanggan</div>
                <div class="signature-line">
                    <?= $pesanan->nama_pelanggan ?>
                </div>
            </div>

            <div class="signature-box">
                <div>Petugas</div>
                <div class="signature-line">
                    <?= $pesanan->created_by ?: '________________' ?>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px; font-size: 10px; color: #999;">
            Dicetak pada: <?= date('d/m/Y H:i:s') ?> |
            <?= $pesanan->nomor_pesanan ?>
        </div>
        <!-- Print Button -->
        <div class="no-print" style="text-align: center; margin-top: 20px;">
            <button onclick="window.print()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                <i class="fas fa-print"></i> Print
            </button>
            <button onclick="window.close()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">
                Close
            </button>
        </div>
    </div>

    <script>
        // Auto print when page loads (optional)
        // window.onload = function() { window.print(); }

        // Close window after printing
        window.onafterprint = function() {
            // window.close();
        }
    </script>
</body>

</html>