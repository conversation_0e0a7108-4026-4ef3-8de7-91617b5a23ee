# DATA DUMMY MODUL PENGIRIMAN - TOKO ELEKTRONIK

## 📋 OVERVIEW

Data dummy lengkap untuk modul pengiriman yang telah dibuat berdasarkan data pesanan yang sudah ada di sistem. Data ini mencakup berbagai skenario pengiriman yang realistis untuk testing dan development.

## 📊 RINGKASAN DATA

### Data Pengiriman yang Dibuat:
- **Total Pengiriman**: 9 pengiriman
- **Periode**: Januari 2025 - Juni 2025
- **Status Beragam**: Draft, <PERSON>ap <PERSON>rim, <PERSON><PERSON>, Terkirim, Selesai, Dibatalkan
- **Jen<PERSON>**: Full dan Partial
- **Ekspedisi**: JNE, Sicepat, J&T, Pos Indonesia, Anteraja

### Distribusi Status:
- ✅ **Selesai**: 3 pengiriman
- 📦 **Terkirim**: 2 pengiriman  
- 🚚 **<PERSON><PERSON>**: 1 pengiriman
- 📋 **Siap Kirim**: 1 pengiriman
- 📝 **Draft**: 1 pengiriman
- ❌ **Dibatalkan**: 1 pengiriman

## 📁 FILE YANG DIBUAT

### 1. `pengiriman_dummy_data.sql`
File utama berisi data dummy lengkap:
- Data pengiriman header (9 records)
- Data pengiriman detail (15 records)
- Data tracking pengiriman (35+ records)
- Data ekspedisi referensi (7 ekspedisi)
- Update status pesanan terkait

### 2. `pengiriman_views_queries.sql`
File tambahan berisi:
- Views untuk reporting dan dashboard
- Stored procedures untuk operasi umum
- Function untuk kalkulasi biaya
- Queries berguna untuk analisis
- Indexes untuk optimasi performa

## 🗂️ STRUKTUR DATA DETAIL

### Tabel Pengiriman (Header)
```sql
pengiriman:
- id (PK)
- nomor_pengiriman (UNIQUE)
- tanggal_pengiriman
- id_pesanan (FK)
- id_pelanggan (FK) 
- id_gudang (FK)
- jenis_pengiriman (full/partial)
- status (draft/siap_kirim/dalam_perjalanan/terkirim/selesai/dibatalkan)
- ekspedisi
- nomor_resi
- biaya_pengiriman
- alamat_pengiriman
- nama_penerima
- telepon_penerima
- catatan_pengiriman
- tanggal_estimasi
- tanggal_terkirim
- total_item, total_qty, total_berat
- timestamps & audit fields
```

### Tabel Pengiriman Detail
```sql
pengiriman_detail:
- id (PK)
- id_pengiriman (FK)
- id_pesanan_detail (FK)
- id_barang (FK)
- qty_dipesan
- qty_dikirim
- qty_sisa (calculated)
- harga_satuan
- subtotal (calculated)
- berat_satuan
- total_berat (calculated)
- keterangan
```

### Tabel Tracking
```sql
pengiriman_tracking:
- id (PK)
- id_pengiriman (FK)
- status
- tanggal_status
- lokasi
- keterangan
- created_by
- created_at
```

## 📋 DETAIL DATA DUMMY

### Pengiriman Berdasarkan Pesanan Existing:

#### 1. SHP-********-0001 (Status: SELESAI)
- **Pesanan**: PSN-********-0001 (PT Elektronik Jaya)
- **Barang**: Samsung Galaxy A54 (2 unit) + Laptop ASUS (1 unit)
- **Ekspedisi**: JNE Express
- **Durasi**: 1 hari (02 Jan - 03 Jan 2025)
- **Tracking**: 5 status lengkap

#### 2. SHP-********-0002 (Status: TERKIRIM)
- **Pesanan**: PSN-********-0002 (PT Mitra Teknologi) - Partial 1
- **Barang**: Smart TV LG (1 unit)
- **Ekspedisi**: Sicepat Express
- **Status**: Terkirim, menunggu konfirmasi penerima

#### 3. SHP-********-0003 (Status: DALAM PERJALANAN)
- **Pesanan**: PSN-********-0002 (PT Mitra Teknologi) - Partial 2
- **Barang**: Headphone Sony (2 unit) + Speaker JBL (2 unit)
- **Ekspedisi**: Sicepat Express
- **Status**: Sedang dalam perjalanan

#### 4. SHP-********-0004 (Status: SELESAI)
- **Pesanan**: PSN-********-0003 (PT Digital Solutions)
- **Barang**: Smart TV (2 unit) + Speaker (2 unit)
- **Ekspedisi**: J&T Express
- **Durasi**: 1 hari

#### 5. SHP-********-0005 (Status: SELESAI)
- **Pesanan**: PSN-********-0004 (PT Elektronik Prima Tangerang)
- **Barang**: Headphone + Gaming Mouse + Power Bank
- **Ekspedisi**: Pos Indonesia
- **Gudang**: Cabang Surabaya

#### 6. SHP-********-0006 (Status: TERKIRIM)
- **Pesanan**: PSN-********-0005 (PT Teknologi Canggih Serpong)
- **Barang**: Kamera Canon + iPad Air (partial)
- **Ekspedisi**: Anteraja
- **Catatan**: Barang fragile, extra protection

#### 7. SHP-********-0007 (Status: SIAP KIRIM)
- **Pesanan**: PSN********0001 (PT Komputer Nusantara)
- **Barang**: Samsung Galaxy A54 (1 unit)
- **Ekspedisi**: JNE Regular
- **Status**: Sudah dikemas, siap kirim

#### 8. SHP-********-0008 (Status: DRAFT)
- **Pesanan**: PSN-********-0001 (Testing)
- **Barang**: Samsung Galaxy A54 (partial)
- **Status**: Masih draft untuk testing

#### 9. SHP-********-0009 (Status: DIBATALKAN)
- **Pesanan**: PSN-********-0002 (Testing)
- **Alasan**: Dibatalkan atas permintaan pelanggan

## 🎯 SKENARIO TESTING

### 1. Pengiriman Full vs Partial
- **Full**: Semua item pesanan dikirim sekaligus
- **Partial**: Item dikirim bertahap (contoh: pesanan 2 dibagi 2 pengiriman)

### 2. Berbagai Status Pengiriman
- **Draft**: Pengiriman baru dibuat, belum diproses
- **Siap Kirim**: Barang sudah dikemas, siap dikirim
- **Dalam Perjalanan**: Sedang dalam perjalanan ke tujuan
- **Terkirim**: Sudah sampai, menunggu konfirmasi penerima
- **Selesai**: Pengiriman selesai, barang diterima pelanggan
- **Dibatalkan**: Pengiriman dibatalkan

### 3. Multi Ekspedisi
- JNE Express (premium, cepat)
- Sicepat Express (ekonomis)
- J&T Express (reguler)
- Pos Indonesia (government)
- Anteraja (startup)

### 4. Multi Gudang
- Gudang Distribusi (Jakarta) - Gudang utama
- Gudang Cabang Surabaya - Gudang regional
- Gudang Spare Parts - Gudang khusus

## 📈 VIEWS DAN REPORTS

### Views yang Tersedia:
1. **v_pengiriman_lengkap**: Data pengiriman dengan info pelanggan dan pesanan
2. **v_dashboard_pengiriman**: Ringkasan harian untuk dashboard
3. **v_tracking_lengkap**: Tracking detail dengan durasi antar status
4. **v_performa_ekspedisi**: Analisis performa masing-masing ekspedisi
5. **v_pengiriman_followup**: Pengiriman yang perlu follow up

### Stored Procedures:
1. **sp_create_pengiriman_from_pesanan**: Membuat pengiriman dari pesanan
2. **sp_update_status_pengiriman**: Update status dengan tracking otomatis

### Functions:
1. **fn_estimasi_biaya_pengiriman**: Kalkulasi estimasi biaya berdasarkan berat

## 🔧 CARA INSTALASI

### 1. Persiapan
```sql
-- Pastikan tabel dasar sudah ada (pesanan, pelanggan, gudang, barang)
-- Jika belum, jalankan script utama terlebih dahulu
```

### 2. Install Tabel Pengiriman
```sql
-- Jalankan file pengiriman_complete.sql untuk membuat tabel
SOURCE pengiriman_complete.sql;
```

### 3. Insert Data Dummy
```sql
-- Jalankan file data dummy
SOURCE pengiriman_dummy_data.sql;
```

### 4. Install Views dan Procedures
```sql
-- Jalankan file views dan queries
SOURCE pengiriman_views_queries.sql;
```

## 📊 CONTOH QUERIES

### Cek Data Pengiriman
```sql
-- Lihat semua pengiriman
SELECT * FROM v_pengiriman_lengkap;

-- Lihat dashboard harian
SELECT * FROM v_dashboard_pengiriman;

-- Lihat tracking lengkap
SELECT * FROM v_tracking_lengkap WHERE nomor_pengiriman = 'SHP-********-0001';
```

### Analisis Performa
```sql
-- Performa ekspedisi
SELECT * FROM v_performa_ekspedisi;

-- Pengiriman yang perlu follow up
SELECT * FROM v_pengiriman_followup;
```

### Laporan
```sql
-- Pengiriman per bulan
SELECT 
    DATE_FORMAT(tanggal_pengiriman, '%Y-%m') as bulan,
    COUNT(*) as total_pengiriman,
    SUM(total_qty) as total_qty,
    SUM(biaya_pengiriman) as total_biaya
FROM pengiriman 
GROUP BY DATE_FORMAT(tanggal_pengiriman, '%Y-%m')
ORDER BY bulan DESC;
```

## 🎨 FITUR KHUSUS

### 1. Auto-Generated Nomor Pengiriman
Format: `SHP-YYYYMMDD-NNNN`
- SHP: Prefix untuk Shipment
- YYYYMMDD: Tanggal pengiriman
- NNNN: Counter harian (0001, 0002, dst)

### 2. Calculated Fields
- `qty_sisa`: Otomatis dihitung dari qty_dipesan - qty_dikirim
- `subtotal`: Otomatis dihitung dari qty_dikirim * harga_satuan
- `total_berat`: Otomatis dihitung dari qty_dikirim * berat_satuan

### 3. Triggers Otomatis
- Update total pengiriman saat detail berubah
- Auto tracking saat status berubah
- Auto create barang keluar saat status "dalam_perjalanan"

### 4. Business Logic
- Pengiriman partial: Bisa ada multiple pengiriman untuk 1 pesanan
- Status progression: Draft → Siap Kirim → Dalam Perjalanan → Terkirim → Selesai
- Auto update status pesanan saat semua pengiriman selesai

## 🚀 PENGGUNAAN

Data dummy ini siap digunakan untuk:
- **Development**: Testing fitur pengiriman
- **Demo**: Presentasi ke client/stakeholder  
- **Training**: Pelatihan user sistem
- **Load Testing**: Testing performa dengan data realistis

## 📞 SUPPORT

Jika ada pertanyaan atau butuh modifikasi data dummy:
1. Cek file SQL untuk detail implementasi
2. Gunakan views yang tersedia untuk analisis
3. Modifikasi sesuai kebutuhan bisnis spesifik

---

**Note**: Data dummy ini dibuat berdasarkan data pesanan yang sudah ada di sistem. Pastikan data pesanan, pelanggan, gudang, dan barang sudah tersedia sebelum menjalankan script pengiriman.