<!-- Detail Table Only - untuk reload tabel tanpa reload seluruh modal -->
<table id="tbl_detail_opname" class="table table-bordered table-striped table-sm">
    <thead class="bg-light">
        <tr>
            <th width="5%">No</th>
            <th width="15%">Kode Barang</th>
            <th width="25%"><PERSON><PERSON></th>
            <th width="12%">Qty Sistem</th>
            <th width="12%">Qty Fisik</th>
            <th width="12%">Selisih</th>
            <th width="15%">Keterangan</th>
            <?php if ($opname->status == 'draft'): ?>
            <th width="10%">Aksi</th>
            <?php endif; ?>
        </tr>
    </thead>
    <tbody>
        <?php if (!empty($detail_list)): ?>
            <?php $no = 1; foreach ($detail_list as $detail): ?>
            <tr>
                <td><?= $no++ ?></td>
                <td><?= $detail->kode_barang ?></td>
                <td><?= $detail->nama_barang ?></td>
                <td class="text-right"><?= number_format($detail->qty_sistem, 0) ?></td>
                <td class="text-right"><?= number_format($detail->qty_fisik, 0) ?></td>
                <td class="text-right">
                    <?php if ($detail->selisih >= 0): ?>
                        <span class="text-success">+<?= number_format($detail->selisih, 0) ?></span>
                    <?php else: ?>
                        <span class="text-danger"><?= number_format($detail->selisih, 0) ?></span>
                    <?php endif; ?>
                </td>
                <td><?= $detail->keterangan ?: '-' ?></td>
                <?php if ($opname->status == 'draft'): ?>
                <td>
                    <button type="button" class="btn btn-xs btn-outline-info" onclick="editDetailItem(<?= $detail->id ?>)" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-xs btn-outline-danger" onclick="deleteDetailItem(<?= $detail->id ?>)" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
                <?php endif; ?>
            </tr>
            <?php endforeach; ?>
        <?php else: ?>
            <tr>
                <td colspan="<?= $opname->status == 'draft' ? '8' : '7' ?>" class="text-center">
                    Belum ada detail item. 
                    <?php if ($opname->status == 'draft'): ?>
                        <a href="javascript:void(0)" onclick="autoGenerateDetail()">Klik di sini untuk auto generate dari stok sistem</a>
                    <?php endif; ?>
                </td>
            </tr>
        <?php endif; ?>
    </tbody>
</table>