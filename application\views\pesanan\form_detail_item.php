<form action="#" id="form_detail" class="form-horizontal">
    <input type="hidden" value="" name="id" />
    <input type="hidden" value="" name="id_pesanan" />
    
    <div class="form-group row">
        <label for="id_barang" class="col-sm-3 col-form-label">Barang <span class="text-danger">*</span></label>
        <div class="col-sm-9 kosong">
            <select class="form-control select2" name="id_barang" id="id_barang" required style="width: 100%;">
                <option value="">-- Pilih Barang --</option>
                <?php if(isset($barang_list)): ?>
                    <?php foreach($barang_list as $barang): ?>
                        <option value="<?= $barang->id ?>" data-harga="<?= $barang->harga_jual ?>" data-satuan="<?= $barang->nama_satuan ?>">
                            <?= $barang->kode_barang ?> - <?= $barang->nama_barang ?> (<?= $barang->merk ?> - <?= $barang->tipe ?>)
                        </option>
                    <?php endforeach; ?>
                <?php endif; ?>
            </select>
            <span class="help-block"></span>
        </div>
    </div>

    <div class="form-group row">
        <label for="qty" class="col-sm-3 col-form-label">Qty <span class="text-danger">*</span></label>
        <div class="col-sm-6 kosong">
            <input type="number" class="form-control" name="qty" id="qty" placeholder="Qty" min="1" step="1" required>
            <span class="help-block"></span>
        </div>
        <div class="col-sm-3">
            <input type="text" class="form-control" id="satuan" placeholder="Satuan" readonly>
        </div>
    </div>

    <div class="form-group row">
        <label for="harga_satuan" class="col-sm-3 col-form-label">Harga Satuan <span class="text-danger">*</span></label>
        <div class="col-sm-9 kosong">
            <input type="number" class="form-control" name="harga_satuan" id="harga_satuan" placeholder="Harga Satuan" min="0" step="0.01" required>
            <span class="help-block"></span>
        </div>
    </div>

    <div class="form-group row">
        <label for="subtotal" class="col-sm-3 col-form-label">Subtotal</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" id="subtotal" placeholder="Subtotal" readonly>
        </div>
    </div>

    <div class="form-group row">
        <label for="keterangan_detail" class="col-sm-3 col-form-label">Keterangan</label>
        <div class="col-sm-9 kosong">
            <textarea class="form-control" name="keterangan" id="keterangan_detail" placeholder="Keterangan item" rows="3"></textarea>
            <span class="help-block"></span>
        </div>
    </div>
</form>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        dropdownParent: $('#modal_detail'),
        placeholder: "-- Pilih --",
        allowClear: true
    });
    
    // Event handler untuk perubahan barang
    $('#id_barang').on('change', function() {
        var selectedOption = $(this).find('option:selected');
        var harga = selectedOption.data('harga');
        var satuan = selectedOption.data('satuan');
        
        $('#harga_satuan').val(harga || 0);
        $('#satuan').val(satuan || '');
        
        calculateSubtotal();
    });
    
    // Event handler untuk perubahan qty dan harga
    $('#qty, #harga_satuan').on('input', function() {
        calculateSubtotal();
    });
    
    function calculateSubtotal() {
        var qty = parseFloat($('#qty').val()) || 0;
        var harga = parseFloat($('#harga_satuan').val()) || 0;
        var subtotal = qty * harga;
        
        $('#subtotal').val(formatRupiah(subtotal));
    }
    
    function formatRupiah(angka) {
        return 'Rp ' + angka.toLocaleString('id-ID');
    }
});
</script>