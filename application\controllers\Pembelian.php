<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Controller Pembelian (Purchase)
 * Mengatur pembelian dari supplier dan detailnya
 * Terintegrasi dengan modul barang, supplier, dan gudang
 */
class Pembelian extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model(array('Mod_pembelian', 'Mod_dashboard'));
        $this->load->library('form_validation');
    }

    public function index()
    {
        $link = $this->uri->segment(1);
        $level = $this->session->userdata('id_level');

        // Cek Posisi Menu apakah Sub Menu Atau bukan
        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();

        if ($jml > 0) { //Jika Menu
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $a_menu = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $a_menu->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $a_submenu = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $a_submenu->view ?? 'N';
        }

        if ($akses == "Y") {
            $this->template->load('layoutbackend', 'pembelian/pembelian', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        ini_set('memory_limit', '512M');
        set_time_limit(3600);
        
        // Set filter parameters
        $this->Mod_pembelian->set_filter([
            'status' => $this->input->post('filter_status'),
            'jenis' => $this->input->post('filter_jenis'),
            'tanggal_dari' => $this->input->post('filter_tanggal_dari'),
            'tanggal_sampai' => $this->input->post('filter_tanggal_sampai')
        ]);
        
        $list = $this->Mod_pembelian->get_datatables();
        $data = array();
        foreach ($list as $pembelian) {
            $row = array();
            $row[] = $pembelian->nomor_pembelian;
            $row[] = date('d/m/Y', strtotime($pembelian->tanggal_pembelian));
            $row[] = $pembelian->nama_supplier . ' (' . $pembelian->kode_supplier . ')';
            
            // Jenis pembelian badge
            switch ($pembelian->jenis_pembelian) {
                case 'reguler':
                    $jenis_badge = '<span class="badge badge-primary">Reguler</span>';
                    break;
                case 'konsinyasi':
                    $jenis_badge = '<span class="badge badge-info">Konsinyasi</span>';
                    break;
                case 'kontrak':
                    $jenis_badge = '<span class="badge badge-warning">Kontrak</span>';
                    break;
                default:
                    $jenis_badge = '<span class="badge badge-secondary">' . ucfirst($pembelian->jenis_pembelian) . '</span>';
            }
            $row[] = $jenis_badge;
            
            // Status badge
            switch ($pembelian->status) {
                case 'draft':
                    $status_badge = '<span class="badge badge-secondary">Draft</span>';
                    break;
                case 'disetujui':
                    $status_badge = '<span class="badge badge-primary">Disetujui</span>';
                    break;
                case 'dipesan':
                    $status_badge = '<span class="badge badge-warning">Dipesan</span>';
                    break;
                case 'diterima':
                    $status_badge = '<span class="badge badge-info">Diterima</span>';
                    break;
                case 'selesai':
                    $status_badge = '<span class="badge badge-success">Selesai</span>';
                    break;
                case 'dibatalkan':
                    $status_badge = '<span class="badge badge-danger">Dibatalkan</span>';
                    break;
                default:
                    $status_badge = '<span class="badge badge-secondary">' . ucfirst($pembelian->status) . '</span>';
            }
            $row[] = $status_badge;

            // Status pembayaran badge
            switch ($pembelian->status_pembayaran) {
                case 'belum_bayar':
                    $bayar_badge = '<span class="badge badge-danger">Belum Bayar</span>';
                    break;
                case 'sebagian':
                    $bayar_badge = '<span class="badge badge-warning">Sebagian</span>';
                    break;
                case 'lunas':
                    $bayar_badge = '<span class="badge badge-success">Lunas</span>';
                    break;
                default:
                    $bayar_badge = '<span class="badge badge-secondary">-</span>';
            }
            $row[] = $bayar_badge;
            
            $row[] = number_format($pembelian->total_item ?? 0, 0) . ' item';
            $row[] = number_format($pembelian->total_qty ?? 0, 0);
            $row[] = 'Rp ' . number_format($pembelian->total_akhir ?? 0, 0, ',', '.');
            
            // Action buttons
            $actions = '';
            if ($pembelian->status == 'draft') {
                $actions .= '<a class="btn btn-xs btn-outline-info edit" href="javascript:void(0)" title="Edit" onclick="edit(' . $pembelian->id . ')"><i class="fas fa-edit"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $pembelian->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-success approve" href="javascript:void(0)" title="Setujui" onclick="updateStatus(' . $pembelian->id . ', \'disetujui\')"><i class="fas fa-check"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-danger delete" href="javascript:void(0)" title="Delete" onclick="hapus(' . $pembelian->id . ')"><i class="fas fa-trash"></i></a>';
            } elseif ($pembelian->status == 'disetujui') {
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $pembelian->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-warning order" href="javascript:void(0)" title="Pesan" onclick="updateStatus(' . $pembelian->id . ', \'dipesan\')"><i class="fas fa-shopping-cart"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-info print" href="javascript:void(0)" title="Print PO" onclick="printPO(' . $pembelian->id . ')"><i class="fas fa-print"></i></a>';
            } elseif ($pembelian->status == 'dipesan') {
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $pembelian->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-success receive" href="javascript:void(0)" title="Terima" onclick="updateStatus(' . $pembelian->id . ', \'diterima\')"><i class="fas fa-truck"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-info print" href="javascript:void(0)" title="Print PO" onclick="printPO(' . $pembelian->id . ')"><i class="fas fa-print"></i></a>';
            } else {
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $pembelian->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-info print" href="javascript:void(0)" title="Print PO" onclick="printPO(' . $pembelian->id . ')"><i class="fas fa-print"></i></a>';
                
                // Tombol pembayaran jika belum lunas
                if ($pembelian->status_pembayaran != 'lunas') {
                    $actions .= ' <a class="btn btn-xs btn-outline-warning payment" href="javascript:void(0)" title="Pembayaran" onclick="showPayment(' . $pembelian->id . ')"><i class="fas fa-money-bill"></i></a>';
                }
            }

            $row[] = $actions;
            
            $data[] = $row;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_pembelian->count_all(),
            "recordsFiltered" => $this->Mod_pembelian->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    // Method untuk form input modal
    public function form_input()
    {
        // Load dropdown data untuk form
        $data['supplier_list'] = $this->Mod_pembelian->get_supplier_aktif();
        $data['nomor_pembelian'] = $this->Mod_pembelian->generate_nomor_pembelian();
        $this->load->view('pembelian/form_input', $data);
    }

    public function generate_nomor()
    {
        $nomor = $this->Mod_pembelian->generate_nomor_pembelian();
        echo json_encode(array('nomor' => $nomor));
    }

    public function get_supplier_info()
    {
        $id = $this->input->post('id');
        $supplier = $this->Mod_pembelian->get_supplier_by_id($id);

        if ($supplier) {
            echo json_encode(array('status' => true, 'data' => $supplier));
        } else {
            echo json_encode(array('status' => false, 'message' => 'Data supplier tidak ditemukan'));
        }
    }

    public function form_detail_item()
    {
        $data['barang_list'] = $this->Mod_pembelian->get_barang_aktif();
        $data['gudang_list'] = $this->Mod_pembelian->get_gudang_aktif();
        $this->load->view('pembelian/form_detail_item', $data);
    }

    public function edit($id)
    {
        $data = $this->Mod_pembelian->get_by_id($id);
        echo json_encode($data);
    }

    public function detail_modal($id)
    {
        $data['pembelian'] = $this->Mod_pembelian->get_by_id($id);
        $data['pembelian_detail'] = $this->Mod_pembelian->get_detail_by_pembelian_id($id);
        $data['barang_list'] = $this->Mod_pembelian->get_barang_aktif();
        $data['gudang_list'] = $this->Mod_pembelian->get_gudang_aktif();
        $data['tracking'] = $this->Mod_pembelian->get_tracking_by_pembelian_id($id);
        $data['pembayaran'] = $this->Mod_pembelian->get_pembayaran_by_pembelian_id($id);
        $this->load->view('pembelian/detail_modal', $data);
    }
    
    public function insert()
    {
        $this->_validate();

        // Generate nomor otomatis jika tidak diisi
        $nomor = $this->input->post('nomor_pembelian');
        if (empty($nomor)) {
            $nomor = $this->Mod_pembelian->generate_nomor_pembelian();
        }

        $save = array(
            'nomor_pembelian' => $nomor,
            'tanggal_pembelian' => $this->input->post('tanggal_pembelian'),
            'id_supplier' => $this->input->post('id_supplier'),
            'jenis_pembelian' => $this->input->post('jenis_pembelian'),
            'status' => $this->input->post('status') ? $this->input->post('status') : 'draft',
            'tanggal_jatuh_tempo' => $this->input->post('tanggal_jatuh_tempo'),
            'syarat_pembayaran' => $this->input->post('syarat_pembayaran'),
            'metode_pembayaran' => $this->input->post('metode_pembayaran'),
            'nomor_po_supplier' => $this->input->post('nomor_po_supplier'),
            'alamat_pengiriman' => $this->input->post('alamat_pengiriman'),
            'keterangan' => $this->input->post('keterangan'),
            'total_item' => 0,
            'total_qty' => 0,
            'total_akhir' => 0,
            'created_by' => $this->session->userdata('nama_user'),
        );

        $this->Mod_pembelian->insert('pembelian', $save);
        echo json_encode(array("status" => TRUE));
    }

    public function update()
    {
        $this->_validate();

        $id = $this->input->post('id');
        $save = array(
            'tanggal_pembelian' => $this->input->post('tanggal_pembelian'),
            'id_supplier' => $this->input->post('id_supplier'),
            'jenis_pembelian' => $this->input->post('jenis_pembelian'),
            'status' => $this->input->post('status'),
            'tanggal_jatuh_tempo' => $this->input->post('tanggal_jatuh_tempo'),
            'syarat_pembayaran' => $this->input->post('syarat_pembayaran'),
            'metode_pembayaran' => $this->input->post('metode_pembayaran'),
            'nomor_po_supplier' => $this->input->post('nomor_po_supplier'),
            'alamat_pengiriman' => $this->input->post('alamat_pengiriman'),
            'keterangan' => $this->input->post('keterangan'),
            'updated_by' => $this->session->userdata('nama_user'),
        );

        $this->Mod_pembelian->update($id, $save);
        echo json_encode(array("status" => TRUE));
    }
    
    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;
        
        if($this->input->post('nomor_pembelian') == '')
        {
            $data['inputerror'][] = 'nomor_pembelian';
            $data['error_string'][] = 'Nomor pembelian tidak boleh kosong';
            $data['status'] = FALSE;
        }
        
        if($this->input->post('tanggal_pembelian') == '')
        {
            $data['inputerror'][] = 'tanggal_pembelian';
            $data['error_string'][] = 'Tanggal pembelian tidak boleh kosong';
            $data['status'] = FALSE;
        }
        
        if($this->input->post('id_supplier') == '')
        {
            $data['inputerror'][] = 'id_supplier';
            $data['error_string'][] = 'Supplier harus dipilih';
            $data['status'] = FALSE;
        }
        
        if($this->input->post('jenis_pembelian') == '')
        {
            $data['inputerror'][] = 'jenis_pembelian';
            $data['error_string'][] = 'Jenis pembelian harus dipilih';
            $data['status'] = FALSE;
        }
        
        if($data['status'] === FALSE)
        {
            echo json_encode($data);
            exit();
        }
    }

    public function delete()
    {
        $id = $this->input->post('id');

        // Cek apakah pembelian masih draft
        $pembelian = $this->Mod_pembelian->get_by_id($id);
        if($pembelian && $pembelian->status != 'draft') {
            echo json_encode(array('status' => FALSE, 'message' => 'Pembelian yang sudah disetujui tidak dapat dihapus!'));
            return;
        }

        // Hapus detail pembelian terlebih dahulu
        $this->Mod_pembelian->delete_detail_by_pembelian($id);

        // Hapus pembelian
        $this->Mod_pembelian->delete($id, 'pembelian');
        echo json_encode(array("status" => TRUE));
    }

    // Detail pembelian functions
    public function add_detail()
    {
        $id_pembelian = $this->input->post('id_pembelian');
        $id_barang = $this->input->post('id_barang');
        $id_gudang = $this->input->post('id_gudang');
        $qty = $this->input->post('qty');
        $harga_satuan = $this->input->post('harga_satuan');
        $diskon_persen = $this->input->post('diskon_persen') ?: 0;
        $diskon_nominal = $this->input->post('diskon_nominal') ?: 0;
        $ppn_persen = $this->input->post('ppn_persen') ?: 11;
        $keterangan = $this->input->post('keterangan');
        
        $subtotal_sebelum_diskon = $qty * $harga_satuan;
        
        // Hitung diskon
        if ($diskon_persen > 0) {
            $diskon_nominal = ($subtotal_sebelum_diskon * $diskon_persen) / 100;
        }
        
        $subtotal_setelah_diskon = $subtotal_sebelum_diskon - $diskon_nominal;
        
        // Hitung PPN
        $ppn_nominal = ($subtotal_setelah_diskon * $ppn_persen) / 100;
        $total_akhir = $subtotal_setelah_diskon + $ppn_nominal;
        
        $data = array(
            'id_pembelian' => $id_pembelian,
            'id_barang' => $id_barang,
            'id_gudang' => $id_gudang,
            'qty' => $qty,
            'harga_satuan' => $harga_satuan,
            'diskon_persen' => $diskon_persen,
            'diskon_nominal' => $diskon_nominal,
            'subtotal_sebelum_diskon' => $subtotal_sebelum_diskon,
            'subtotal_setelah_diskon' => $subtotal_setelah_diskon,
            'ppn_persen' => $ppn_persen,
            'ppn_nominal' => $ppn_nominal,
            'total_akhir' => $total_akhir,
            'keterangan' => $keterangan
        );
        
        $insert = $this->Mod_pembelian->save_detail($data);
        if ($insert) {
            echo json_encode(array('status' => 'success', 'message' => 'Item berhasil ditambahkan!'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Item gagal ditambahkan!'));
        }
    }

    public function update_detail()
    {
        $id = $this->input->post('id');
        $id_pembelian = $this->input->post('id_pembelian');
        $qty = $this->input->post('qty');
        $harga_satuan = $this->input->post('harga_satuan');
        $diskon_persen = $this->input->post('diskon_persen') ?: 0;
        $diskon_nominal = $this->input->post('diskon_nominal') ?: 0;
        $ppn_persen = $this->input->post('ppn_persen') ?: 11;
        $keterangan = $this->input->post('keterangan');
        
        $subtotal_sebelum_diskon = $qty * $harga_satuan;
        
        // Hitung diskon
        if ($diskon_persen > 0) {
            $diskon_nominal = ($subtotal_sebelum_diskon * $diskon_persen) / 100;
        }
        
        $subtotal_setelah_diskon = $subtotal_sebelum_diskon - $diskon_nominal;
        
        // Hitung PPN
        $ppn_nominal = ($subtotal_setelah_diskon * $ppn_persen) / 100;
        $total_akhir = $subtotal_setelah_diskon + $ppn_nominal;
        
        $data = array(
            'qty' => $qty,
            'harga_satuan' => $harga_satuan,
            'diskon_persen' => $diskon_persen,
            'diskon_nominal' => $diskon_nominal,
            'subtotal_sebelum_diskon' => $subtotal_sebelum_diskon,
            'subtotal_setelah_diskon' => $subtotal_setelah_diskon,
            'ppn_persen' => $ppn_persen,
            'ppn_nominal' => $ppn_nominal,
            'total_akhir' => $total_akhir,
            'keterangan' => $keterangan
        );
        
        $update = $this->Mod_pembelian->update_detail(array('id' => $id), $data);
        if ($update) {
            echo json_encode(array('status' => 'success', 'message' => 'Item berhasil diupdate!'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Item gagal diupdate!'));
        }
    }

    public function delete_detail()
    {
        $id = $this->input->post('id');
        
        $this->Mod_pembelian->delete_detail($id);
        
        echo json_encode(array('status' => 'success', 'message' => 'Item berhasil dihapus!'));
    }

    public function get_detail_item()
    {
        $id = $this->input->post('id');
        
        $detail = $this->Mod_pembelian->get_detail_by_id($id);
        if ($detail) {
            echo json_encode(array('status' => 'success', 'data' => $detail));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Data item tidak ditemukan!'));
        }
    }

    public function update_status()
    {
        $id = $this->input->post('id');
        $status = $this->input->post('status');
        
        $data = array(
            'status' => $status,
            'updated_by' => $this->session->userdata('nama_user'),
            'updated_at' => date('Y-m-d H:i:s')
        );

        // Jika status disetujui, set approved_by dan approved_at
        if ($status == 'disetujui') {
            $data['approved_by'] = $this->session->userdata('nama_user');
            $data['approved_at'] = date('Y-m-d H:i:s');
        }
        
        $update = $this->Mod_pembelian->update($id, $data);
        if ($update) {
            echo json_encode(array('status' => 'success', 'message' => 'Status pembelian berhasil diupdate!'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Status pembelian gagal diupdate!'));
        }
    }

    public function print_po($id)
    {
        $data['pembelian'] = $this->Mod_pembelian->get_by_id($id);
        if (!$data['pembelian']) {
            show_404();
        }

        $data['detail'] = $this->Mod_pembelian->get_detail_by_pembelian_id($id);
        $this->load->view('pembelian/print_po', $data);
    }

    // Pembayaran functions
    public function form_pembayaran($id_pembelian)
    {
        $data['pembelian'] = $this->Mod_pembelian->get_by_id($id_pembelian);
        $data['nomor_pembayaran'] = $this->Mod_pembelian->generate_nomor_pembayaran();
        $this->load->view('pembelian/form_pembayaran', $data);
    }

    public function save_pembayaran()
    {
        $id_pembelian = $this->input->post('id_pembelian');
        $nomor_pembayaran = $this->input->post('nomor_pembayaran');
        $tanggal_pembayaran = $this->input->post('tanggal_pembayaran');
        $jumlah_bayar = $this->input->post('jumlah_bayar');
        $metode_pembayaran = $this->input->post('metode_pembayaran');
        $nomor_referensi = $this->input->post('nomor_referensi');
        $bank_pengirim = $this->input->post('bank_pengirim');
        $bank_penerima = $this->input->post('bank_penerima');
        $keterangan = $this->input->post('keterangan');

        $data = array(
            'id_pembelian' => $id_pembelian,
            'nomor_pembayaran' => $nomor_pembayaran,
            'tanggal_pembayaran' => $tanggal_pembayaran,
            'jumlah_bayar' => $jumlah_bayar,
            'metode_pembayaran' => $metode_pembayaran,
            'nomor_referensi' => $nomor_referensi,
            'bank_pengirim' => $bank_pengirim,
            'bank_penerima' => $bank_penerima,
            'keterangan' => $keterangan,
            'status' => 'verified', // Auto verified untuk demo
            'created_by' => $this->session->userdata('nama_user'),
            'verified_by' => $this->session->userdata('nama_user'),
            'verified_at' => date('Y-m-d H:i:s')
        );

        $insert = $this->Mod_pembelian->save_pembayaran($data);
        if ($insert) {
            echo json_encode(array('status' => 'success', 'message' => 'Pembayaran berhasil disimpan!'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Pembayaran gagal disimpan!'));
        }
    }

    // Export functions
    public function export($format = 'excel')
    {
        // Set filter parameters
        $this->Mod_pembelian->set_filter([
            'status' => $this->input->get('filter_status'),
            'jenis' => $this->input->get('filter_jenis'),
            'tanggal_dari' => $this->input->get('filter_tanggal_dari'),
            'tanggal_sampai' => $this->input->get('filter_tanggal_sampai')
        ]);

        $data = $this->Mod_pembelian->get_all_for_export();
        
        if ($format == 'excel') {
            $this->export_excel($data);
        } elseif ($format == 'pdf') {
            $this->export_pdf($data);
        } else {
            show_404();
        }
    }

    private function export_excel($data)
    {
        $this->load->library('excel');
        
        $objPHPExcel = new PHPExcel();
        $objPHPExcel->setActiveSheetIndex(0);
        $sheet = $objPHPExcel->getActiveSheet();
        
        // Set header
        $sheet->setCellValue('A1', 'LAPORAN DATA PEMBELIAN');
        $sheet->mergeCells('A1:J1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        
        // Set column headers
        $headers = [
            'A3' => 'No',
            'B3' => 'Nomor Pembelian',
            'C3' => 'Tanggal',
            'D3' => 'Supplier',
            'E3' => 'Jenis',
            'F3' => 'Status',
            'G3' => 'Total Item',
            'H3' => 'Total Qty',
            'I3' => 'Total Harga',
            'J3' => 'Status Pembayaran'
        ];
        
        foreach ($headers as $cell => $header) {
            $sheet->setCellValue($cell, $header);
            $sheet->getStyle($cell)->getFont()->setBold(true);
        }
        
        // Set data
        $row = 4;
        $no = 1;
        foreach ($data as $item) {
            $sheet->setCellValue('A' . $row, $no++);
            $sheet->setCellValue('B' . $row, $item->nomor_pembelian);
            $sheet->setCellValue('C' . $row, date('d/m/Y', strtotime($item->tanggal_pembelian)));
            $sheet->setCellValue('D' . $row, $item->nama_supplier);
            $sheet->setCellValue('E' . $row, ucfirst($item->jenis_pembelian));
            $sheet->setCellValue('F' . $row, ucfirst($item->status));
            $sheet->setCellValue('G' . $row, $item->total_item);
            $sheet->setCellValue('H' . $row, $item->total_qty);
            $sheet->setCellValue('I' . $row, $item->total_akhir);
            $sheet->setCellValue('J' . $row, ucfirst($item->status_pembayaran));
            $row++;
        }
        
        // Auto size columns
        foreach (range('A', 'J') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
        
        // Set filename and download
        $filename = 'Laporan_Pembelian_' . date('Y-m-d_H-i-s') . '.xlsx';
        
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        
        $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
        $objWriter->save('php://output');
    }

    private function export_pdf($data)
    {
        $this->load->library('pdf');
        
        $html = '<h2 style="text-align: center;">LAPORAN DATA PEMBELIAN</h2>';
        $html .= '<table border="1" cellpadding="5" cellspacing="0" style="width: 100%; font-size: 10px;">';
        $html .= '<thead>';
        $html .= '<tr style="background-color: #f0f0f0;">';
        $html .= '<th>No</th>';
        $html .= '<th>Nomor Pembelian</th>';
        $html .= '<th>Tanggal</th>';
        $html .= '<th>Supplier</th>';
        $html .= '<th>Jenis</th>';
        $html .= '<th>Status</th>';
        $html .= '<th>Total Item</th>';
        $html .= '<th>Total Qty</th>';
        $html .= '<th>Total Harga</th>';
        $html .= '<th>Status Pembayaran</th>';
        $html .= '</tr>';
        $html .= '</thead>';
        $html .= '<tbody>';
        
        $no = 1;
        foreach ($data as $item) {
            $html .= '<tr>';
            $html .= '<td>' . $no++ . '</td>';
            $html .= '<td>' . $item->nomor_pembelian . '</td>';
            $html .= '<td>' . date('d/m/Y', strtotime($item->tanggal_pembelian)) . '</td>';
            $html .= '<td>' . $item->nama_supplier . '</td>';
            $html .= '<td>' . ucfirst($item->jenis_pembelian) . '</td>';
            $html .= '<td>' . ucfirst($item->status) . '</td>';
            $html .= '<td>' . $item->total_item . '</td>';
            $html .= '<td>' . $item->total_qty . '</td>';
            $html .= '<td>Rp ' . number_format($item->total_akhir, 0, ',', '.') . '</td>';
            $html .= '<td>' . ucfirst($item->status_pembayaran) . '</td>';
            $html .= '</tr>';
        }
        
        $html .= '</tbody>';
        $html .= '</table>';
        
        $filename = 'Laporan_Pembelian_' . date('Y-m-d_H-i-s') . '.pdf';
        
        $this->pdf->loadHtml($html);
        $this->pdf->setPaper('A4', 'landscape');
        $this->pdf->render();
        $this->pdf->stream($filename, array('Attachment' => 1));
    }
}