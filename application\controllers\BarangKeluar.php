<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Controller <PERSON><PERSON>
 * Mengatur pengeluaran barang keluar dan detailnya
 */
class BarangKeluar extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model(array('Mod_barang_keluar', 'Mod_dashboard'));
        $this->load->library('form_validation');
    }

    public function index()
    {
        $link = $this->uri->segment(1);
        $level = $this->session->userdata('id_level');

        // Cek Posisi Menu apakah Sub Menu Atau bukan
        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        
        if ($jml > 0) { //Jika Menu
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $a_menu = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $a_menu->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $a_submenu = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $a_submenu->view ?? 'N';
        }

        if ($akses == "Y") {
            $this->template->load('layoutbackend', 'barang_keluar/barang_keluar', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        ini_set('memory_limit', '512M');
        set_time_limit(3600);
        $list = $this->Mod_barang_keluar->get_datatables();
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $barang_keluar) {
            $no++;
            $row = array();
            $row[] = $barang_keluar->nomor_pengeluaran;
            $row[] = date('d/m/Y', strtotime($barang_keluar->tanggal));
            $row[] = $barang_keluar->nama_pelanggan ? 
                     $barang_keluar->nama_pelanggan . '<br><small class="text-muted">' . $barang_keluar->kode_pelanggan . '</small>' : 
                     '<span class="text-muted">-</span>';
            
            // Jenis dengan badge
            $jenis_class = 'badge-primary';
            switch($barang_keluar->jenis) {
                case 'penjualan': $jenis_class = 'badge-primary'; break;
                case 'retur_pembelian': $jenis_class = 'badge-info'; break;
                case 'transfer_keluar': $jenis_class = 'badge-warning'; break;
                case 'rusak': $jenis_class = 'badge-danger'; break;
                case 'hilang': $jenis_class = 'badge-dark'; break;
                default: $jenis_class = 'badge-secondary'; break;
            }
            $row[] = '<span class="badge ' . $jenis_class . '">' . strtoupper(str_replace('_', ' ', $barang_keluar->jenis)) . '</span>';
            
            $row[] = $barang_keluar->ref_nomor ?: '-';
            
            // Status dengan badge
            $status_class = $barang_keluar->status == 'final' ? 'badge-success' : 'badge-warning';
            $row[] = '<span class="badge ' . $status_class . '">' . strtoupper($barang_keluar->status) . '</span>';
            
            $row[] = number_format($barang_keluar->total_item, 0);
            $row[] = number_format($barang_keluar->total_qty, 0);

            // Action buttons
            $actions = '';
            if ($barang_keluar->status == 'draft') {
                $actions .= '<a class="btn btn-xs btn-outline-info edit" href="javascript:void(0)" title="Edit" onclick="edit(' . $barang_keluar->id . ')"><i class="fas fa-edit"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $barang_keluar->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-success finalize" href="javascript:void(0)" title="Finalisasi" onclick="finalize(' . $barang_keluar->id . ')"><i class="fas fa-check"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-danger delete" href="javascript:void(0)" title="Delete" onclick="hapus(' . $barang_keluar->id . ')"><i class="fas fa-trash"></i></a>';
            } else {
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $barang_keluar->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-info print" href="javascript:void(0)" title="Print" onclick="print_barang_keluar(' . $barang_keluar->id . ')"><i class="fas fa-print"></i></a>';
            }
            
            $row[] = $actions;
            $data[] = $row;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_barang_keluar->count_all(),
            "recordsFiltered" => $this->Mod_barang_keluar->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function insert()
    {
        $this->_validate();

        // Generate nomor otomatis jika tidak diisi
        $nomor = $this->input->post('nomor_pengeluaran');
        if (empty($nomor)) {
            $nomor = $this->Mod_barang_keluar->generate_nomor();
        }

        $save = array(
            'nomor_pengeluaran' => $nomor,
            'tanggal' => $this->input->post('tanggal'),
            'id_pelanggan' => $this->input->post('id_pelanggan') ?: null,
            'jenis' => $this->input->post('jenis'),
            'ref_nomor' => $this->input->post('ref_nomor'),
            'keterangan' => $this->input->post('keterangan'),
            'created_by' => $this->session->userdata('id_user'),
        );
        
        $id_barang_keluar = $this->Mod_barang_keluar->insert($save);
        
        echo json_encode(array(
            "status" => TRUE,
            "id_barang_keluar" => $id_barang_keluar
        ));
    }

    public function update()
    {
        $this->_validate();

        $id = $this->input->post('id');

        $save = array(
            'tanggal' => $this->input->post('tanggal'),
            'id_pelanggan' => $this->input->post('id_pelanggan') ?: null,
            'jenis' => $this->input->post('jenis'),
            'ref_nomor' => $this->input->post('ref_nomor'),
            'keterangan' => $this->input->post('keterangan'),
        );

        $this->Mod_barang_keluar->update($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit($id)
    {
        $data = $this->Mod_barang_keluar->get($id);
        echo json_encode($data);
    }

    public function form_input()
    {
        // Load dropdown data untuk form
        $data['pelanggan_list'] = $this->Mod_barang_keluar->get_pelanggan_dropdown();
        $this->load->view('barang_keluar/form_input', $data);
    }

    public function delete()
    {
        $id = $this->input->post('id');
        $this->Mod_barang_keluar->delete($id);
        echo json_encode(array("status" => TRUE));
    }

    public function generate_nomor()
    {
        $nomor = $this->Mod_barang_keluar->generate_nomor();
        echo json_encode(array('nomor' => $nomor));
    }

    // Finalisasi barang keluar
    public function finalize()
    {
        $id = $this->input->post('id');
        $user_final = $this->session->userdata('id_user');
        
        $result = $this->Mod_barang_keluar->finalize_barang_keluar($id, $user_final);
        
        if ($result['valid']) {
            echo json_encode(array("status" => TRUE, "message" => $result['message']));
        } else {
            echo json_encode(array("status" => FALSE, "message" => $result['message'], "insufficient_items" => $result['insufficient_items'] ?? null));
        }
    }

    // ===== DETAIL BARANG KELUAR METHODS =====

    public function detail($id)
    {
        $data['barang_keluar'] = $this->Mod_barang_keluar->get($id);
        $data['detail_list'] = $this->Mod_barang_keluar->get_detail($id);
        $data['barang_list'] = $this->Mod_barang_keluar->get_barang_dropdown();
        $data['gudang_list'] = $this->Mod_barang_keluar->get_gudang_dropdown();
        $data['satuan_list'] = $this->Mod_barang_keluar->get_satuan_dropdown();
        $this->load->view('barang_keluar/detail_barang_keluar', $data);
    }

    public function insert_detail()
    {
        $this->_validate_detail();

        $save = array(
            'id_barang_keluar' => $this->input->post('id_barang_keluar'),
            'id_barang' => $this->input->post('id_barang'),
            'id_gudang' => $this->input->post('id_gudang'),
            'qty_keluar' => $this->input->post('qty_keluar'),
            'id_satuan' => $this->input->post('id_satuan'),
            'harga_satuan' => $this->input->post('harga_satuan') ?: 0,
            'keterangan' => $this->input->post('keterangan'),
        );
        
        $this->Mod_barang_keluar->insert_detail($save);
        echo json_encode(array("status" => TRUE));
    }

    public function update_detail()
    {
        $this->_validate_detail();

        $id = $this->input->post('id');

        $save = array(
            'id_barang' => $this->input->post('id_barang'),
            'id_gudang' => $this->input->post('id_gudang'),
            'qty_keluar' => $this->input->post('qty_keluar'),
            'id_satuan' => $this->input->post('id_satuan'),
            'harga_satuan' => $this->input->post('harga_satuan') ?: 0,
            'keterangan' => $this->input->post('keterangan'),
        );

        $this->Mod_barang_keluar->update_detail($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit_detail($id)
    {
        $data = $this->Mod_barang_keluar->get_detail_by_id($id);
        echo json_encode($data);
    }

    public function delete_detail()
    {
        $id = $this->input->post('id');
        $this->Mod_barang_keluar->delete_detail($id);
        echo json_encode(array("status" => TRUE));
    }

    public function get_barang_detail()
    {
        $id_barang = $this->input->post('id_barang');
        $data = $this->Mod_barang_keluar->get_barang_detail($id_barang);
        echo json_encode($data);
    }

    public function get_stok_barang()
    {
        $id_barang = $this->input->post('id_barang');
        $id_gudang = $this->input->post('id_gudang');
        $stok = $this->Mod_barang_keluar->get_stok_barang($id_barang, $id_gudang);
        echo json_encode(array('stok_tersedia' => $stok));
    }

    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        $nomor = $this->input->post('nomor_pengeluaran');
        $id = $this->input->post('id');

        // Validasi nomor pengeluaran
        if (empty($nomor)) {
            // Nomor boleh kosong, akan di-generate otomatis
        } else {
            if (!preg_match('/^BK-[0-9]{4}-[0-9]{2}-[0-9]{3}$/', $nomor)) {
                $data['inputerror'][] = 'nomor_pengeluaran';
                $data['error_string'][] = 'Format nomor harus BK-2025-01-001, BK-2025-01-002, dst.';
                $data['status'] = FALSE;
            } else if ($this->Mod_barang_keluar->check_nomor_exists($nomor, $id)) {
                $data['inputerror'][] = 'nomor_pengeluaran';
                $data['error_string'][] = 'Nomor pengeluaran sudah ada';
                $data['status'] = FALSE;
            }
        }

        // Validasi tanggal
        if (empty($this->input->post('tanggal'))) {
            $data['inputerror'][] = 'tanggal';
            $data['error_string'][] = 'Tanggal wajib diisi';
            $data['status'] = FALSE;
        }

        // Validasi jenis
        if (empty($this->input->post('jenis'))) {
            $data['inputerror'][] = 'jenis';
            $data['error_string'][] = 'Jenis barang keluar wajib dipilih';
            $data['status'] = FALSE;
        }

        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }

    private function _validate_detail()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        $id_barang_keluar = $this->input->post('id_barang_keluar');
        $id_barang = $this->input->post('id_barang');
        $id_gudang = $this->input->post('id_gudang');
        $qty_keluar = $this->input->post('qty_keluar');
        $id_detail = $this->input->post('id');

        // Validasi barang
        if (empty($id_barang)) {
            $data['inputerror'][] = 'id_barang';
            $data['error_string'][] = 'Barang wajib dipilih';
            $data['status'] = FALSE;
        }

        // Validasi gudang
        if (empty($id_gudang)) {
            $data['inputerror'][] = 'id_gudang';
            $data['error_string'][] = 'Gudang wajib dipilih';
            $data['status'] = FALSE;
        }

        // Validasi qty keluar
        if (empty($qty_keluar) && $qty_keluar !== '0') {
            $data['inputerror'][] = 'qty_keluar';
            $data['error_string'][] = 'Qty keluar wajib diisi';
            $data['status'] = FALSE;
        } else if (!is_numeric($qty_keluar) || $qty_keluar <= 0) {
            $data['inputerror'][] = 'qty_keluar';
            $data['error_string'][] = 'Qty keluar harus berupa angka positif';
            $data['status'] = FALSE;
        }

        // Validasi stok tersedia
        if (!empty($id_barang) && !empty($id_gudang) && is_numeric($qty_keluar) && $qty_keluar > 0) {
            $stok_tersedia = $this->Mod_barang_keluar->get_stok_barang($id_barang, $id_gudang);
            if ($qty_keluar > $stok_tersedia) {
                $data['inputerror'][] = 'qty_keluar';
                $data['error_string'][] = 'Qty keluar (' . number_format($qty_keluar, 0) . ') melebihi stok tersedia (' . number_format($stok_tersedia, 0) . ')';
                $data['status'] = FALSE;
            }
        }

        // Validasi duplicate detail
        if (!empty($id_barang_keluar) && !empty($id_barang) && !empty($id_gudang)) {
            if ($this->Mod_barang_keluar->check_duplicate_detail($id_barang_keluar, $id_barang, $id_gudang, $id_detail)) {
                $data['inputerror'][] = 'id_barang';
                $data['error_string'][] = 'Kombinasi barang dan gudang sudah ada dalam transaksi ini';
                $data['status'] = FALSE;
            }
        }

        // Validasi harga satuan (opsional tapi harus angka jika diisi)
        $harga_satuan = $this->input->post('harga_satuan');
        if (!empty($harga_satuan) && (!is_numeric($harga_satuan) || $harga_satuan < 0)) {
            $data['inputerror'][] = 'harga_satuan';
            $data['error_string'][] = 'Harga satuan harus berupa angka positif';
            $data['status'] = FALSE;
        }

        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }

    // Get detail items untuk refresh tabel
    public function get_detail_items($id_barang_keluar)
    {
        $data = $this->Mod_barang_keluar->get_detail($id_barang_keluar);
        echo json_encode($data);
    }

    // Print Barang Keluar
    public function cetak_barang_keluar($id)
    {
        $data['barang_keluar'] = $this->Mod_barang_keluar->get($id);
        $data['detail_list'] = $this->Mod_barang_keluar->get_detail($id);
        $this->load->view('barang_keluar/cetak_barang_keluar', $data);
    }
}
