<?php
/**
 * Test Form Pengiriman - Standalone Test
 * Test form pengiriman tanpa dependency CodeIgniter
 */

// Simulate data yang biasanya dari database
$pesanan_list = array(
    (object) array(
        'id' => 1,
        'nomor_pesanan' => 'PES-20250108-0001',
        'nama_pelanggan' => 'PT. Teknologi Maju',
        'alamat_pelanggan' => 'Jl. Sudirman No. 123, Jakarta',
        'no_telepon' => '021-12345678',
        'tanggal_pesanan' => '2025-01-08'
    ),
    (object) array(
        'id' => 2,
        'nomor_pesanan' => 'PES-20250108-0002',
        'nama_pelanggan' => 'CV. Digital Solutions',
        'alamat_pelanggan' => 'Jl. Gatot Subroto No. 456, Jakarta',
        'no_telepon' => '021-87654321',
        'tanggal_pesanan' => '2025-01-08'
    )
);

$pelanggan_list = array(
    (object) array(
        'id' => 1,
        'kode' => 'PLG001',
        'nama' => 'PT. Teknologi Maju'
    ),
    (object) array(
        'id' => 2,
        'kode' => 'PLG002',
        'nama' => 'CV. Digital Solutions'
    )
);

$nomor_pengiriman = 'PGR-' . date('Ymd') . '-0001';

// Function untuk simulate site_url
function site_url($uri = '') {
    return 'http://localhost/toko_elektronik/' . $uri;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Form Pengiriman</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/pengiriman.css" rel="stylesheet">
    <link href="assets/css/status-badges.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4><i class="fa fa-shipping-fast"></i> Test Form Pengiriman</h4>
                </div>
                <div class="card-body">
                    
                    <!-- Include form content -->
                    <form action="#" id="form" class="form-horizontal">
                        <input type="hidden" value="" name="id" />
                        <div class="card-body">

                            <!-- Tab Navigation -->
                            <ul class="nav nav-tabs" id="pengirimanTab" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link active" id="basic-tab" data-toggle="tab" href="#basic" role="tab">Data Dasar</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="pesanan-tab" data-toggle="tab" href="#pesanan" role="tab">Pesanan & Pelanggan</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="ekspedisi-tab" data-toggle="tab" href="#ekspedisi" role="tab">Ekspedisi & Tracking</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="keterangan-tab" data-toggle="tab" href="#keterangan" role="tab">Keterangan</a>
                                </li>
                            </ul>

                            <!-- Tab Content -->
                            <div class="tab-content" id="pengirimanTabContent">

                                <!-- Tab Data Dasar -->
                                <div class="tab-pane fade show active" id="basic" role="tabpanel">
                                    <div class="pt-3">

                                        <div class="form-group row">
                                            <label for="nomor_pengiriman" class="col-sm-3 col-form-label">Nomor Pengiriman <span class="text-danger">*</span></label>
                                            <div class="col-sm-9">
                                                <div class="input-group">
                                                    <input type="text" class="form-control" name="nomor_pengiriman" id="nomor_pengiriman" 
                                                           placeholder="Nomor akan digenerate otomatis" value="<?= $nomor_pengiriman ?>" readonly>
                                                    <div class="input-group-append">
                                                        <button type="button" class="btn btn-outline-secondary" id="btn-generate-nomor" onclick="generateNomor()">
                                                            <i class="fa fa-sync"></i> Generate
                                                        </button>
                                                    </div>
                                                </div>
                                                <small class="form-text text-muted">Nomor pengiriman akan digenerate otomatis jika kosong.</small>
                                                <span class="help-block"></span>
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label for="tanggal_pengiriman" class="col-sm-3 col-form-label">Tanggal Pengiriman <span class="text-danger">*</span></label>
                                            <div class="col-sm-9">
                                                <input type="date" class="form-control" name="tanggal_pengiriman" id="tanggal_pengiriman" required>
                                                <span class="help-block"></span>
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label for="status" class="col-sm-3 col-form-label">Status</label>
                                            <div class="col-sm-9">
                                                <select class="form-control select2" name="status" id="status" style="width: 100%;">
                                                    <option value="draft">Draft</option>
                                                    <option value="prepared">Prepared</option>
                                                    <option value="shipped">Shipped</option>
                                                    <option value="in_transit">In Transit</option>
                                                    <option value="delivered">Delivered</option>
                                                    <option value="returned">Returned</option>
                                                    <option value="cancelled">Cancelled</option>
                                                </select>
                                                <small class="form-text text-muted">Status pengiriman saat ini.</small>
                                                <span class="help-block"></span>
                                            </div>
                                        </div>

                                    </div>
                                </div>

                                <!-- Tab Pesanan & Pelanggan -->
                                <div class="tab-pane fade" id="pesanan" role="tabpanel">
                                    <div class="pt-3">

                                        <div class="form-group row">
                                            <label for="id_pesanan" class="col-sm-3 col-form-label">Pesanan <span class="text-danger">*</span></label>
                                            <div class="col-sm-9">
                                                <select class="form-control select2" name="id_pesanan" id="id_pesanan" style="width: 100%;" required>
                                                    <option value="">-- Pilih Pesanan --</option>
                                                    <?php if (isset($pesanan_list) && is_array($pesanan_list)): ?>
                                                        <?php foreach ($pesanan_list as $pesanan): ?>
                                                            <option value="<?= $pesanan->id ?>" 
                                                                    data-pelanggan="<?= $pesanan->nama_pelanggan ?>"
                                                                    data-alamat="<?= $pesanan->alamat_pelanggan ?>"
                                                                    data-telepon="<?= $pesanan->no_telepon ?>">
                                                                <?= $pesanan->nomor_pesanan ?> - <?= $pesanan->nama_pelanggan ?> 
                                                                (<?= date('d/m/Y', strtotime($pesanan->tanggal_pesanan)) ?>)
                                                            </option>
                                                        <?php endforeach; ?>
                                                    <?php else: ?>
                                                        <option value="" disabled>Tidak ada pesanan yang tersedia</option>
                                                    <?php endif; ?>
                                                </select>
                                                <small class="form-text text-muted">Pilih pesanan yang akan dikirim. Hanya pesanan dengan status 'diproses' yang ditampilkan.</small>
                                                <span class="help-block"></span>
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label for="id_pelanggan" class="col-sm-3 col-form-label">Pelanggan <span class="text-danger">*</span></label>
                                            <div class="col-sm-9">
                                                <select class="form-control select2" name="id_pelanggan" id="id_pelanggan" style="width: 100%;" required>
                                                    <option value="">-- Pilih Pelanggan --</option>
                                                    <?php if (isset($pelanggan_list) && is_array($pelanggan_list)): ?>
                                                        <?php foreach ($pelanggan_list as $pelanggan): ?>
                                                            <option value="<?= $pelanggan->id ?>"><?= $pelanggan->kode ?> - <?= $pelanggan->nama ?></option>
                                                        <?php endforeach; ?>
                                                    <?php else: ?>
                                                        <option value="" disabled>Tidak ada pelanggan yang tersedia</option>
                                                    <?php endif; ?>
                                                </select>
                                                <small class="form-text text-muted">Pelanggan akan terisi otomatis saat memilih pesanan.</small>
                                                <span class="help-block"></span>
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label for="alamat_pengiriman" class="col-sm-3 col-form-label">Alamat Pengiriman <span class="text-danger">*</span></label>
                                            <div class="col-sm-9">
                                                <textarea class="form-control" name="alamat_pengiriman" id="alamat_pengiriman" 
                                                          placeholder="Alamat lengkap untuk pengiriman" rows="3" required></textarea>
                                                <small class="form-text text-muted">Alamat akan terisi otomatis dari data pelanggan, dapat diubah sesuai kebutuhan.</small>
                                                <span class="help-block"></span>
                                            </div>
                                        </div>

                                    </div>
                                </div>

                                <!-- Tab Ekspedisi & Tracking -->
                                <div class="tab-pane fade" id="ekspedisi" role="tabpanel">
                                    <div class="pt-3">

                                        <div class="form-group row">
                                            <label for="ekspedisi" class="col-sm-3 col-form-label">Ekspedisi</label>
                                            <div class="col-sm-9">
                                                <input type="text" class="form-control" name="ekspedisi" id="ekspedisi" 
                                                       placeholder="Nama ekspedisi (JNE, TIKI, POS, dll)">
                                                <small class="form-text text-muted">Nama perusahaan ekspedisi yang digunakan.</small>
                                                <span class="help-block"></span>
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label for="nomor_resi" class="col-sm-3 col-form-label">Nomor Resi</label>
                                            <div class="col-sm-9">
                                                <input type="text" class="form-control" name="nomor_resi" id="nomor_resi" 
                                                       placeholder="Nomor resi dari ekspedisi">
                                                <small class="form-text text-muted">Nomor resi untuk tracking pengiriman.</small>
                                                <span class="help-block"></span>
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label for="biaya_pengiriman" class="col-sm-3 col-form-label">Biaya Pengiriman</label>
                                            <div class="col-sm-9">
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">Rp</span>
                                                    </div>
                                                    <input type="number" class="form-control" name="biaya_pengiriman" id="biaya_pengiriman" 
                                                           placeholder="0" min="0" step="0.01">
                                                </div>
                                                <small class="form-text text-muted">Biaya pengiriman yang dikenakan.</small>
                                                <span class="help-block"></span>
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label for="estimasi_tiba" class="col-sm-3 col-form-label">Estimasi Tiba</label>
                                            <div class="col-sm-9">
                                                <input type="date" class="form-control" name="estimasi_tiba" id="estimasi_tiba">
                                                <small class="form-text text-muted">Perkiraan tanggal barang sampai di tujuan.</small>
                                                <span class="help-block"></span>
                                            </div>
                                        </div>

                                    </div>
                                </div>

                                <!-- Tab Keterangan -->
                                <div class="tab-pane fade" id="keterangan" role="tabpanel">
                                    <div class="pt-3">

                                        <div class="form-group row">
                                            <label for="keterangan" class="col-sm-3 col-form-label">Keterangan</label>
                                            <div class="col-sm-9">
                                                <textarea class="form-control" name="keterangan" id="keterangan" 
                                                          placeholder="Keterangan tambahan mengenai pengiriman ini (opsional)" rows="4"></textarea>
                                                <small class="form-text text-muted">Informasi tambahan mengenai pengiriman ini.</small>
                                                <span class="help-block"></span>
                                            </div>
                                        </div>

                                    </div>
                                </div>

                            </div>

                        </div>
                    </form>

                    <!-- Test Status Badges -->
                    <div class="mt-4">
                        <h5>Test Status Badges:</h5>
                        <span class="badge badge-draft">Draft</span>
                        <span class="badge badge-prepared">Prepared</span>
                        <span class="badge badge-shipped">Shipped</span>
                        <span class="badge badge-in_transit">In Transit</span>
                        <span class="badge badge-delivered">Delivered</span>
                        <span class="badge badge-returned">Returned</span>
                        <span class="badge badge-cancelled">Cancelled</span>
                    </div>

                    <div class="mt-3">
                        <button type="button" class="btn btn-primary" onclick="testForm()">
                            <i class="fa fa-save"></i> Test Save
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                            <i class="fa fa-refresh"></i> Reset Form
                        </button>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        placeholder: "-- Pilih --",
        allowClear: true
    });

    // Set tanggal hari ini sebagai default
    var today = new Date().toISOString().split('T')[0];
    $('#tanggal_pengiriman').val(today);

    // Event handler untuk perubahan pesanan
    $('#id_pesanan').on('change', function() {
        var selectedOption = $(this).find('option:selected');
        var pelangganNama = selectedOption.data('pelanggan');
        var alamat = selectedOption.data('alamat');
        var telepon = selectedOption.data('telepon');

        if (pelangganNama) {
            // Find pelanggan ID by name and set it
            $('#id_pelanggan option').each(function() {
                if ($(this).text().includes(pelangganNama)) {
                    $('#id_pelanggan').val($(this).val()).trigger('change');
                    return false;
                }
            });
            
            // Set alamat pengiriman
            if (alamat) {
                $('#alamat_pengiriman').val(alamat);
            }
        }
    });
});

function generateNomor() {
    var newNomor = 'PGR-' + new Date().toISOString().slice(0,10).replace(/-/g,"") + '-' + 
                   String(Math.floor(Math.random() * 9999) + 1).padStart(4, '0');
    $('#nomor_pengiriman').val(newNomor);
    alert('Nomor baru: ' + newNomor);
}

function testForm() {
    var formData = $('#form').serialize();
    alert('Form data: ' + formData);
    console.log('Form data:', formData);
}

function resetForm() {
    $('#form')[0].reset();
    $('.select2').val('').trigger('change');
    var today = new Date().toISOString().split('T')[0];
    $('#tanggal_pengiriman').val(today);
}
</script>

</body>
</html>
