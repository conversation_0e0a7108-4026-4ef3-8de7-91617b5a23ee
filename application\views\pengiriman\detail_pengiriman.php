<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        
        <!-- Header Info -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary">
                        <h3 class="card-title text-white">
                            <i class="fa fa-shipping-fast"></i> Detail Pengiriman: <?= $pengiriman_data->nomor_pengiriman ?>
                        </h3>
                        <div class="card-tools">
                            <a href="<?= site_url('pengiriman') ?>" class="btn btn-sm btn-light">
                                <i class="fa fa-arrow-left"></i> Kembali
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td width="150"><strong>Nomor <PERSON>giriman</strong></td>
                                        <td>: <?= $pengiriman_data->nomor_pengiriman ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Tanggal</strong></td>
                                        <td>: <?= date('d/m/Y', strtotime($pengiriman_data->tanggal_pengiriman)) ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Pesanan</strong></td>
                                        <td>: <?= $pengiriman_data->nomor_pesanan ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Pelanggan</strong></td>
                                        <td>: <?= $pengiriman_data->nama_pelanggan ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status</strong></td>
                                        <td>: <span class="badge badge-<?= $pengiriman_data->status ?>"><?= ucfirst($pengiriman_data->status) ?></span></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td width="150"><strong>Ekspedisi</strong></td>
                                        <td>: <?= $pengiriman_data->ekspedisi ?: '-' ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Nomor Resi</strong></td>
                                        <td>: <?= $pengiriman_data->nomor_resi ?: '-' ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Total Item</strong></td>
                                        <td>: <?= number_format($pengiriman_data->total_item, 0) ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Total Qty</strong></td>
                                        <td>: <?= number_format($pengiriman_data->total_qty, 2) ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Total Berat</strong></td>
                                        <td>: <?= number_format($pengiriman_data->total_berat, 2) ?> kg</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <?php if ($pengiriman_data->alamat_pengiriman): ?>
                        <div class="row mt-3">
                            <div class="col-12">
                                <strong>Alamat Pengiriman:</strong><br>
                                <?= nl2br($pengiriman_data->alamat_pengiriman) ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($pengiriman_data->keterangan): ?>
                        <div class="row mt-3">
                            <div class="col-12">
                                <strong>Keterangan:</strong><br>
                                <?= nl2br($pengiriman_data->keterangan) ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detail Items -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fa fa-list text-blue"></i> Detail Item Pengiriman</h3>
                        <div class="text-right">
                            <?php if ($pengiriman_data->status == 'draft'): ?>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="add_detail()" title="Tambah Item">
                                    <i class="fas fa-plus"></i> Tambah Item
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-success" onclick="load_from_pesanan()" title="Load dari Pesanan">
                                    <i class="fas fa-download"></i> Load dari Pesanan
                                </button>
                            <?php endif; ?>
                            
                            <?php if ($pengiriman_data->status == 'prepared'): ?>
                                <button type="button" class="btn btn-sm btn-success" onclick="finalize_pengiriman()" title="Finalisasi Pengiriman">
                                    <i class="fas fa-check"></i> Finalisasi Pengiriman
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="card-body">
                        <table id="tbl_detail" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr class="bg-info">
                                    <th width="50">No</th>
                                    <th>Barang</th>
                                    <th>Gudang</th>
                                    <th>Qty Dikirim</th>
                                    <th>Berat Satuan</th>
                                    <th>Total Berat</th>
                                    <th>Keterangan</th>
                                    <?php if ($pengiriman_data->status == 'draft'): ?>
                                    <th width="100">Aksi</th>
                                    <?php endif; ?>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

    </div>
</section>

<!-- Modal Form Detail -->
<div class="modal fade" id="modal_form_detail" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <h4 class="modal-title text-white">Form Detail Pengiriman</h4>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form action="#" id="form_detail" class="form-horizontal">
                    <input type="hidden" value="" name="detail_id" />
                    <input type="hidden" value="<?= $id_pengiriman ?>" name="id_pengiriman" />
                    
                    <div class="form-group row">
                        <label for="id_barang" class="col-sm-3 col-form-label">Barang <span class="text-danger">*</span></label>
                        <div class="col-sm-9">
                            <select class="form-control select2" name="id_barang" id="id_barang" style="width: 100%;" required>
                                <option value="">-- Pilih Barang --</option>
                                <?php foreach ($barang_list as $barang): ?>
                                    <option value="<?= $barang->id ?>" data-satuan="<?= $barang->nama_satuan ?>">
                                        <?= $barang->kode_barang ?> - <?= $barang->nama_barang ?>
                                        <?php if ($barang->merk): ?>(<?= $barang->merk ?>)<?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="id_gudang" class="col-sm-3 col-form-label">Gudang <span class="text-danger">*</span></label>
                        <div class="col-sm-9">
                            <select class="form-control select2" name="id_gudang" id="id_gudang" style="width: 100%;" required>
                                <option value="">-- Pilih Gudang --</option>
                            </select>
                            <small class="form-text text-muted">Hanya gudang dengan stok tersedia yang ditampilkan.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="qty_dikirim" class="col-sm-3 col-form-label">Qty Dikirim <span class="text-danger">*</span></label>
                        <div class="col-sm-9">
                            <div class="input-group">
                                <input type="number" class="form-control" name="qty_dikirim" id="qty_dikirim" 
                                       placeholder="0" min="0.01" step="0.01" required>
                                <div class="input-group-append">
                                    <span class="input-group-text" id="satuan_display">-</span>
                                </div>
                            </div>
                            <small class="form-text text-muted">Jumlah barang yang akan dikirim.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="berat_satuan" class="col-sm-3 col-form-label">Berat Satuan (kg)</label>
                        <div class="col-sm-9">
                            <input type="number" class="form-control" name="berat_satuan" id="berat_satuan" 
                                   placeholder="0.00" min="0" step="0.01">
                            <small class="form-text text-muted">Berat per satuan barang dalam kilogram.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="keterangan_detail" class="col-sm-3 col-form-label">Keterangan</label>
                        <div class="col-sm-9">
                            <textarea class="form-control" name="keterangan_detail" id="keterangan_detail" 
                                      placeholder="Keterangan tambahan (opsional)" rows="3"></textarea>
                            <span class="help-block"></span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSaveDetail" onclick="save_detail()" class="btn btn-primary">
                    <i class="fa fa-save"></i> Simpan
                </button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fa fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    var table_detail;
    var save_method_detail;

    $(document).ready(function() {
        // Initialize Select2
        $('.select2').select2({
            dropdownParent: $('#modal_form_detail'),
            placeholder: "-- Pilih --",
            allowClear: true
        });
        
        // Load detail table
        load_detail_table();

        // Event handler untuk perubahan barang
        $('#id_barang').on('change', function() {
            var selectedOption = $(this).find('option:selected');
            var satuan = selectedOption.data('satuan');
            var id_barang = $(this).val();

            // Update satuan display
            $('#satuan_display').text(satuan || '-');

            // Load gudang dengan stok untuk barang ini
            if (id_barang) {
                load_gudang_with_stock(id_barang);
            } else {
                $('#id_gudang').empty().append('<option value="">-- Pilih Gudang --</option>').trigger('change');
            }
        });
    });

    function load_detail_table() {
        table_detail = $("#tbl_detail").DataTable({
            "responsive": true,
            "autoWidth": false,
            "language": {
                "sEmptyTable": "Belum ada detail item pengiriman"
            },
            "processing": true,
            "ajax": {
                "url": "<?php echo site_url('pengiriman/ajax_list_detail/' . $id_pengiriman) ?>",
                "type": "GET"
            },
            "columnDefs": [
                <?php if ($pengiriman_data->status == 'draft'): ?>
                {
                    "targets": [-1],
                    "orderable": false,
                }
                <?php else: ?>
                {
                    "targets": [],
                    "orderable": true,
                }
                <?php endif; ?>
            ],
        });
    }

    function load_gudang_with_stock(id_barang) {
        $.ajax({
            url: "<?php echo site_url('pengiriman/get_gudang_with_stock') ?>",
            type: "POST",
            data: { id_barang: id_barang },
            dataType: "JSON",
            success: function(data) {
                var options = '<option value="">-- Pilih Gudang --</option>';
                $.each(data, function(index, gudang) {
                    options += '<option value="' + gudang.id + '" data-stok="' + gudang.stok_tersedia + '">' +
                               gudang.nama_gudang + ' (Stok: ' + gudang.stok_tersedia + ')</option>';
                });
                $('#id_gudang').html(options).trigger('change');
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log('Error loading gudang:', textStatus);
                $('#id_gudang').html('<option value="">-- Error loading gudang --</option>').trigger('change');
            }
        });
    }

    function add_detail() {
        save_method_detail = 'add';
        $('.form-group').removeClass('has-error');
        $('.help-block').empty();
        $('.modal-title').text('Tambah Detail Pengiriman');

        // Reset form
        $('#form_detail')[0].reset();
        $('[name="detail_id"]').val('');
        $('#id_barang').val('').trigger('change');
        $('#id_gudang').val('').trigger('change');
        $('#satuan_display').text('-');

        $('#modal_form_detail').modal('show');
    }

    function edit_detail(id) {
        save_method_detail = 'update';
        $('.form-group').removeClass('has-error');
        $('.help-block').empty();
        $('.modal-title').text('Edit Detail Pengiriman');

        // Load data for edit
        $.ajax({
            url: "<?php echo site_url('pengiriman/edit_detail/') ?>" + id,
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                $('[name="detail_id"]').val(data.id);
                $('#id_barang').val(data.id_barang).trigger('change');

                // Wait for gudang to load, then set value
                setTimeout(function() {
                    $('#id_gudang').val(data.id_gudang).trigger('change');
                }, 500);

                $('#qty_dikirim').val(data.qty_dikirim);
                $('#berat_satuan').val(data.berat_satuan);
                $('#keterangan_detail').val(data.keterangan);

                $('#modal_form_detail').modal('show');
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat data detail.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function save_detail() {
        $('#btnSaveDetail').text('Menyimpan...');
        $('#btnSaveDetail').attr('disabled', true);

        var url;
        if (save_method_detail == 'add') {
            url = "<?php echo site_url('pengiriman/insert_detail') ?>";
        } else {
            url = "<?php echo site_url('pengiriman/update_detail') ?>";
        }

        $.ajax({
            url: url,
            type: "POST",
            data: $('#form_detail').serialize(),
            dataType: "JSON",
            success: function(data) {
                if (data.status) {
                    $('#modal_form_detail').modal('hide');
                    table_detail.ajax.reload(null, false);

                    // Reload page to update header totals
                    setTimeout(function() {
                        location.reload();
                    }, 1000);

                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Detail pengiriman berhasil disimpan.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                } else {
                    if (data.message) {
                        Swal.fire({
                            title: 'Error!',
                            text: data.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    } else {
                        for (var i = 0; i < data.inputerror.length; i++) {
                            $('[name="' + data.inputerror[i] + '"]').parent().parent().addClass('has-error');
                            $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]);
                        }
                    }
                }
                $('#btnSaveDetail').text('Simpan');
                $('#btnSaveDetail').attr('disabled', false);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menyimpan data.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                $('#btnSaveDetail').text('Simpan');
                $('#btnSaveDetail').attr('disabled', false);
            }
        });
    }

    function hapus_detail(id) {
        Swal.fire({
            title: 'Konfirmasi',
            text: 'Apakah Anda yakin ingin menghapus detail ini?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: "<?php echo site_url('pengiriman/hapus_detail/') ?>" + id,
                    type: "POST",
                    dataType: "JSON",
                    success: function(data) {
                        table_detail.ajax.reload(null, false);

                        // Reload page to update header totals
                        setTimeout(function() {
                            location.reload();
                        }, 1000);

                        Swal.fire({
                            title: 'Berhasil!',
                            text: 'Detail pengiriman berhasil dihapus.',
                            icon: 'success',
                            confirmButtonText: 'OK'
                        });
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat menghapus data.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }

    function load_from_pesanan() {
        Swal.fire({
            title: 'Load Item dari Pesanan',
            text: 'Fitur ini akan memuat semua item dari pesanan yang belum dikirim. Apakah Anda ingin melanjutkan?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Ya, Load!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                // Show loading
                Swal.fire({
                    title: 'Memuat Item...',
                    text: 'Sedang memuat item dari pesanan, mohon tunggu.',
                    icon: 'info',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Call backend to load items
                $.ajax({
                    url: "<?php echo site_url('pengiriman/load_items_from_pesanan') ?>",
                    type: "POST",
                    data: {
                        id_pengiriman: <?= $id_pengiriman ?>
                    },
                    dataType: "JSON",
                    success: function(data) {
                        Swal.close();

                        if (data.status) {
                            // Reload detail table
                            table_detail.ajax.reload(null, false);

                            // Reload page to update header totals
                            setTimeout(function() {
                                location.reload();
                            }, 1500);

                            Swal.fire({
                                title: 'Berhasil!',
                                text: data.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: data.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.close();
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat memuat item dari pesanan.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }

    function finalize_pengiriman() {
        Swal.fire({
            title: 'Finalisasi Pengiriman',
            text: 'Setelah difinalisasi, pengiriman tidak dapat diubah lagi dan stok akan dikurangi. Apakah Anda yakin?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Ya, Finalisasi!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: "<?php echo site_url('pengiriman/finalize/' . $id_pengiriman) ?>",
                    type: "POST",
                    dataType: "JSON",
                    success: function(data) {
                        if (data.status) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: data.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: data.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat finalisasi pengiriman.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }
</script>
