# Modul <PERSON> (Shipping Module) - Toko Elektronik

## Overview
Modul Pengiriman adalah sistem manajemen pengiriman barang yang terintegrasi dengan modul pesanan dan stok gudang. Modul ini memungkinkan pengelolaan pengiriman barang berdasarkan pesanan pelanggan dengan validasi stok real-time dan tracking pengiriman.

## Features

### Core Functionality
- **Order-based Shipping**: Pengiriman selalu berdasarkan pesanan yang sudah ada
- **Multi-warehouse Support**: Satu item bisa dikirim dari berbagai gudang
- **Partial Shipping**: Pesanan bisa dikirim bertahap sesuai ketersediaan stok
- **Stock Validation**: Validasi stok real-time sebelum pengiriman
- **Status Tracking**: Tracking status pengiriman dari draft hingga delivered

### User Interface
- **Tabbed Form Interface**: Form input dengan tab untuk organisasi data yang lebih baik
- **Responsive Design**: <PERSON>ain yang responsif untuk berbagai ukuran layar
- **Consistent Styling**: <PERSON>gi<PERSON><PERSON> desain dan CSS styling modul pesanan
- **DataTables Integration**: Tabel data dengan fitur sorting, searching, dan pagination

### Workflow Management
1. **Create Shipment**: Pilih pesanan → Buat pengiriman baru
2. **Add Items**: Tambah item dengan pilihan gudang berdasarkan stok tersedia
3. **Validate Stock**: Sistem otomatis validasi ketersediaan stok
4. **Process Shipment**: Finalisasi pengiriman dan update stok
5. **Track Delivery**: Update status pengiriman hingga delivered

## Database Structure

### Tables
- **pengiriman**: Tabel utama pengiriman
- **pengiriman_detail**: Detail item yang dikirim
- **Views**: v_pengiriman_summary, v_pengiriman_detail
- **Triggers**: Auto-update totals saat detail berubah

### Status Flow
```
draft → prepared → shipped → in_transit → delivered
                     ↓
                  returned
                     ↓
                 cancelled
```

## File Structure

```
application/
├── controllers/
│   └── Pengiriman.php              # Main controller
├── models/
│   └── Mod_pengiriman.php          # Model with business logic
├── views/
│   └── pengiriman/
│       ├── pengiriman.php          # Main list view
│       ├── form_input.php          # Add/edit form with tabs
│       └── detail_pengiriman.php   # Detail management page
assets/
├── css/
│   └── pengiriman.css              # Custom styling
DB/
├── pengiriman_setup.sql            # Complete database setup
└── toko_elektronik.sql             # Updated main database
```

## Installation

### 1. Database Setup
```sql
-- Run the pengiriman setup script
SOURCE DB/pengiriman_setup.sql;

-- Or manually execute the SQL commands in the file
```

### 2. File Deployment
Copy all files to their respective directories:
- Controllers → `application/controllers/`
- Models → `application/models/`
- Views → `application/views/pengiriman/`
- CSS → `assets/css/`

### 3. Menu Configuration
Add menu item to your navigation system:
```php
// Example menu configuration
$menu_items[] = array(
    'title' => 'Pengiriman',
    'url' => 'pengiriman',
    'icon' => 'fa-shipping-fast',
    'submenu' => array()
);
```

## Usage Guide

### Creating a New Shipment

1. **Access Module**: Navigate to Pengiriman menu
2. **Add New**: Click "Add" button
3. **Fill Basic Data**:
   - Nomor pengiriman (auto-generated)
   - Tanggal pengiriman
   - Status (default: draft)

4. **Select Order & Customer**:
   - Pilih pesanan dari dropdown
   - Pelanggan akan terisi otomatis
   - Alamat pengiriman dapat disesuaikan

5. **Shipping Details**:
   - Ekspedisi (JNE, TIKI, POS, dll)
   - Nomor resi
   - Biaya pengiriman
   - Estimasi tiba

6. **Save**: Simpan pengiriman

### Managing Shipment Details

1. **Access Detail**: Click detail button on shipment list
2. **Add Items**: 
   - Click "Tambah Item"
   - Pilih barang
   - Sistem akan menampilkan gudang dengan stok tersedia
   - Input quantity dan berat
3. **Stock Validation**: Sistem otomatis validasi stok
4. **Multiple Warehouses**: Item yang sama bisa ditambah dari gudang berbeda
5. **Finalize**: Setelah semua item ditambah, finalisasi pengiriman

### Status Management

- **Draft**: Pengiriman baru, bisa diedit
- **Prepared**: Siap dikirim, menunggu finalisasi
- **Shipped**: Sudah dikirim, stok sudah dikurangi
- **In Transit**: Dalam perjalanan
- **Delivered**: Sudah sampai tujuan
- **Returned**: Dikembalikan
- **Cancelled**: Dibatalkan

## Technical Details

### Stock Integration
- Real-time stock checking dari tabel `stok_barang`
- Validasi stok tersedia vs stok yang sudah direservasi
- Auto-update stok saat pengiriman difinalisasi
- Integration dengan `stok_movement` untuk tracking

### Validation Rules
- Pesanan harus dalam status "diproses"
- Quantity tidak boleh melebihi stok tersedia
- Quantity tidak boleh melebihi quantity pesanan
- Gudang harus memiliki stok untuk barang yang dipilih

### Performance Optimization
- Database indexes untuk query performance
- Efficient joins untuk data retrieval
- Pagination untuk large datasets
- Caching untuk dropdown data

## API Endpoints

### Main Endpoints
- `GET /pengiriman` - List pengiriman
- `POST /pengiriman/ajax_list` - DataTables data
- `GET /pengiriman/form_input` - Form input
- `POST /pengiriman/insert` - Create new
- `POST /pengiriman/update` - Update existing
- `GET /pengiriman/edit/{id}` - Get data for edit
- `POST /pengiriman/hapus/{id}` - Delete

### Detail Endpoints
- `GET /pengiriman/detail/{id}` - Detail page
- `POST /pengiriman/ajax_list_detail/{id}` - Detail items
- `POST /pengiriman/insert_detail` - Add detail item
- `POST /pengiriman/update_detail` - Update detail item
- `POST /pengiriman/hapus_detail/{id}` - Delete detail item

### Utility Endpoints
- `GET /pengiriman/generate_nomor` - Generate nomor pengiriman
- `POST /pengiriman/get_pesanan_detail` - Get pesanan items
- `POST /pengiriman/get_gudang_with_stock` - Get warehouses with stock
- `POST /pengiriman/finalize/{id}` - Finalize shipment

## Customization

### Adding New Status
1. Update enum in database table
2. Add CSS class in `pengiriman.css`
3. Update `get_status_class()` method in controller
4. Update status dropdown in form

### Custom Validation
Extend `_validate()` and `_validate_detail()` methods in controller:
```php
private function _validate_custom() {
    // Add custom validation rules
    if ($this->input->post('custom_field') == '') {
        $data['inputerror'][] = 'custom_field';
        $data['error_string'][] = 'Custom field required';
        $data['status'] = FALSE;
    }
}
```

### Additional Fields
1. Add fields to database table
2. Update model methods
3. Add form fields in view
4. Update validation rules

## Troubleshooting

### Common Issues

1. **Stock Validation Errors**
   - Check `stok_barang` table data
   - Verify gudang is active
   - Check for reserved stock in draft shipments

2. **Permission Errors**
   - Verify database user permissions
   - Check foreign key constraints
   - Ensure proper table relationships

3. **Performance Issues**
   - Check database indexes
   - Optimize queries in model
   - Consider caching for large datasets

### Debug Mode
Enable debug mode in CodeIgniter for detailed error messages:
```php
// In index.php
define('ENVIRONMENT', 'development');
```

## Support

For technical support or feature requests:
1. Check existing documentation
2. Review database structure
3. Test with sample data
4. Contact development team

## Version History

- **v1.0.0**: Initial release with core functionality
- **v1.1.0**: Added multi-warehouse support
- **v1.2.0**: Enhanced stock validation
- **v1.3.0**: Improved UI/UX with tabs

## License

This module is part of the Toko Elektronik application and follows the same licensing terms.
