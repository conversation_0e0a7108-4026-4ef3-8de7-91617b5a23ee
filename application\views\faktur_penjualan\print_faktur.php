<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faktur Penjualan - <?= $faktur->nomor_faktur ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
            color: #333;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .company-info {
            font-size: 11px;
            color: #666;
        }

        .invoice-title {
            font-size: 20px;
            font-weight: bold;
            margin: 20px 0 10px 0;
            text-transform: uppercase;
        }

        .invoice-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }

        .invoice-details,
        .customer-details {
            width: 48%;
        }

        .invoice-details h4,
        .customer-details h4 {
            margin: 0 0 10px 0;
            font-size: 14px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }

        .detail-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .detail-table th {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            font-weight: bold;
        }

        .detail-table td {
            border: 1px solid #ddd;
            padding: 8px;
            vertical-align: top;
        }

        .detail-table .text-right {
            text-align: right;
        }

        .detail-table .text-center {
            text-align: center;
        }

        .summary-table {
            width: 300px;
            margin-left: auto;
            margin-top: 20px;
        }

        .summary-table td {
            padding: 5px 10px;
            border: none;
        }

        .summary-table .total-row {
            border-top: 2px solid #333;
            font-weight: bold;
            font-size: 14px;
        }

        .footer {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
        }

        .signature {
            text-align: center;
            width: 200px;
        }

        .signature-line {
            border-top: 1px solid #333;
            margin-top: 60px;
            padding-top: 5px;
        }

        .notes {
            margin-top: 30px;
            padding: 15px;
            background-color: #f9f9f9;
            border-left: 4px solid #007bff;
        }

        .notes h5 {
            margin: 0 0 10px 0;
            font-size: 13px;
        }

        .tax-info {
            margin-top: 20px;
            font-size: 11px;
            color: #666;
            text-align: center;
        }

        @media print {
            body {
                margin: 0;
                padding: 15px;
            }

            .no-print {
                display: none;
            }
        }

        .badge {
            display: inline-block;
            padding: 3px 8px;
            font-size: 10px;
            font-weight: bold;
            border-radius: 3px;
            text-transform: uppercase;
        }

        .badge-pajak {
            background-color: #007bff;
            color: white;
        }

        .badge-reguler {
            background-color: #6c757d;
            color: white;
        }

        .status-badge {
            float: right;
            margin-top: -5px;
        }
    </style>
</head>

<body>

    <!-- Header -->
    <div class="header">
        <div class="company-name"><?= $aplikasi->nama_aplikasi ?? 'TOKO ELEKTRONIK' ?></div>
        <div class="company-info">
            <?= $aplikasi->alamat ?? 'Alamat Perusahaan' ?><br>
            Telp: <?= $aplikasi->no_telepon ?? '-' ?> | Email: <?= $aplikasi->email ?? '-' ?><br>
            <?php if ($aplikasi->npwp ?? false): ?>
                NPWP: <?= $aplikasi->npwp ?>
            <?php endif; ?>
        </div>

        <div class="invoice-title">
            <?= $faktur->jenis_faktur == 'pajak' ? 'FAKTUR PAJAK' : 'FAKTUR PENJUALAN' ?>
            <span class="badge <?= $faktur->jenis_faktur == 'pajak' ? 'badge-pajak' : 'badge-reguler' ?> status-badge">
                <?= ucfirst($faktur->status) ?>
            </span>
        </div>
    </div>

    <!-- Invoice Info -->
    <div class="invoice-info">
        <div class="invoice-details">
            <h4>DETAIL FAKTUR</h4>
            <table style="width: 100%; font-size: 12px;">
                <tr>
                    <td width="120"><strong>Nomor Faktur</strong></td>
                    <td width="10">:</td>
                    <td><?= $faktur->nomor_faktur ?></td>
                </tr>
                <tr>
                    <td><strong>Tanggal Faktur</strong></td>
                    <td>:</td>
                    <td><?= date('d/m/Y', strtotime($faktur->tanggal_faktur)) ?></td>
                </tr>
                <tr>
                    <td><strong>Jatuh Tempo</strong></td>
                    <td>:</td>
                    <td><?= $faktur->tanggal_jatuh_tempo ? date('d/m/Y', strtotime($faktur->tanggal_jatuh_tempo)) : '-' ?></td>
                </tr>
                <tr>
                    <td><strong>Syarat Pembayaran</strong></td>
                    <td>:</td>
                    <td><?= $faktur->syarat_pembayaran ?: '-' ?></td>
                </tr>
                <tr>
                    <td><strong>Metode Pembayaran</strong></td>
                    <td>:</td>
                    <td><?= ucfirst($faktur->metode_pembayaran) ?></td>
                </tr>
                <?php if ($faktur->nomor_po_pelanggan): ?>
                    <tr>
                        <td><strong>No. PO Pelanggan</strong></td>
                        <td>:</td>
                        <td><?= $faktur->nomor_po_pelanggan ?></td>
                    </tr>
                <?php endif; ?>
                <?php if ($faktur->sales_person): ?>
                    <tr>
                        <td><strong>Sales Person</strong></td>
                        <td>:</td>
                        <td><?= $faktur->sales_person ?></td>
                    </tr>
                <?php endif; ?>
            </table>
        </div>

        <div class="customer-details">
            <h4>KEPADA</h4>
            <table style="width: 100%; font-size: 12px;">
                <tr>
                    <td width="80"><strong>Nama</strong></td>
                    <td width="10">:</td>
                    <td><?= $faktur->nama_pelanggan ?></td>
                </tr>
                <tr>
                    <td><strong>Alamat</strong></td>
                    <td>:</td>
                    <td><?= $faktur->alamat_penagihan ?: $faktur->alamat_pelanggan ?></td>
                </tr>
                <tr>
                    <td><strong>Telepon</strong></td>
                    <td>:</td>
                    <td><?= $faktur->telepon_pelanggan ?: '-' ?></td>
                </tr>
                <tr>
                    <td><strong>Email</strong></td>
                    <td>:</td>
                    <td><?= $faktur->email_pelanggan ?: '-' ?></td>
                </tr>
                <?php if ($faktur->npwp): ?>
                    <tr>
                        <td><strong>NPWP</strong></td>
                        <td>:</td>
                        <td><?= $faktur->npwp ?></td>
                    </tr>
                <?php endif; ?>
                <?php if ($faktur->jenis_faktur == 'pajak'): ?>
                    <tr>
                        <td><strong>PKP</strong></td>
                        <td>:</td>
                        <td><?= $faktur->is_pkp ? 'Ya' : 'Tidak' ?></td>
                    </tr>
                <?php endif; ?>
            </table>
        </div>
    </div>

    <!-- Detail Items -->
    <table class="detail-table">
        <thead>
            <tr>
                <th width="5%" class="text-center">No</th>
                <th width="15%">Kode Barang</th>
                <th width="30%">Nama Barang</th>
                <th width="8%" class="text-center">Qty</th>
                <th width="8%">Satuan</th>
                <th width="12%" class="text-right">Harga Satuan</th>
                <th width="10%" class="text-center">Diskon</th>
                <th width="12%" class="text-right">Total</th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($faktur_detail)): ?>
                <?php $no = 1;
                foreach ($faktur_detail as $d): ?>
                    <tr>
                        <td class="text-center"><?= $no++ ?></td>
                        <td><?= $d->kode_barang ?></td>
                        <td>
                            <strong><?= $d->nama_barang ?></strong>
                            <?php if ($d->merk): ?>
                                <br><small style="color: #666;"><?= $d->merk ?> <?= $d->tipe ?></small>
                            <?php endif; ?>
                            <?php if ($d->keterangan): ?>
                                <br><small style="color: #666; font-style: italic;"><?= $d->keterangan ?></small>
                            <?php endif; ?>
                        </td>
                        <td class="text-center"><?= number_format($d->qty, 0) ?></td>
                        <td><?= $d->nama_satuan ?></td>
                        <td class="text-right">Rp <?= number_format($d->harga_satuan, 0, ',', '.') ?></td>
                        <td class="text-center">
                            <?php if ($d->diskon_persen > 0): ?>
                                <?= $d->diskon_persen ?>%
                            <?php endif; ?>
                            <?php if ($d->diskon_nominal > 0): ?>
                                <?php if ($d->diskon_persen > 0): ?><br><?php endif; ?>
                                Rp <?= number_format($d->diskon_nominal, 0, ',', '.') ?>
                            <?php endif; ?>
                            <?php if ($d->diskon_persen == 0 && $d->diskon_nominal == 0): ?>
                                -
                            <?php endif; ?>
                        </td>
                        <td class="text-right">Rp <?= number_format($d->total_akhir, 0, ',', '.') ?></td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="8" class="text-center" style="padding: 30px; color: #666;">
                        Tidak ada item dalam faktur ini
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>

    <!-- Summary -->
    <table class="summary-table">
        <tr>
            <td><strong>Total Item:</strong></td>
            <td class="text-right"><?= number_format($faktur->total_item, 0) ?></td>
        </tr>
        <tr>
            <td><strong>Total Quantity:</strong></td>
            <td class="text-right"><?= number_format($faktur->total_qty, 0) ?></td>
        </tr>
        <tr>
            <td><strong>Subtotal:</strong></td>
            <td class="text-right">Rp <?= number_format($faktur->subtotal, 0, ',', '.') ?></td>
        </tr>
        <?php if ($faktur->diskon_persen > 0 || $faktur->diskon_nominal > 0): ?>
            <tr>
                <td><strong>Diskon:</strong></td>
                <td class="text-right">
                    <?php if ($faktur->diskon_persen > 0): ?>
                        (<?= $faktur->diskon_persen ?>%)
                    <?php endif; ?>
                    <?php if ($faktur->diskon_nominal > 0): ?>
                        Rp <?= number_format($faktur->diskon_nominal, 0, ',', '.') ?>
                    <?php endif; ?>
                </td>
            </tr>
            <tr>
                <td><strong>Setelah Diskon:</strong></td>
                <td class="text-right">Rp <?= number_format($faktur->total_sebelum_pajak, 0, ',', '.') ?></td>
            </tr>
        <?php endif; ?>
        <?php if ($faktur->ppn_nominal > 0): ?>
            <tr>
                <td><strong>PPN (<?= $faktur->ppn_persen ?>%):</strong></td>
                <td class="text-right">Rp <?= number_format($faktur->ppn_nominal, 0, ',', '.') ?></td>
            </tr>
        <?php endif; ?>
        <tr class="total-row">
            <td><strong>TOTAL AKHIR:</strong></td>
            <td class="text-right"><strong>Rp <?= number_format($faktur->total_setelah_pajak, 0, ',', '.') ?></strong></td>
        </tr>
    </table>

    <?php if ($faktur->keterangan): ?>
        <!-- Notes -->
        <div class="notes">
            <h5>KETERANGAN:</h5>
            <?= nl2br(htmlspecialchars($faktur->keterangan)) ?>
        </div>
    <?php endif; ?>

    <!-- Footer -->
    <div class="footer">
        <div class="signature">
            <div>Pelanggan</div>
            <div class="signature-line">
                (&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)
            </div>
        </div>

        <div class="signature">
            <div>Hormat Kami</div>
            <div class="signature-line">
                (&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)
            </div>
        </div>
    </div>

    <?php if ($faktur->jenis_faktur == 'pajak'): ?>
        <!-- Tax Info -->
        <div class="tax-info">
            <strong>FAKTUR PAJAK</strong><br>
            Faktur ini merupakan bukti pungutan pajak yang sah sesuai dengan Undang-Undang PPN yang berlaku.<br>
            Barang yang sudah dibeli tidak dapat dikembalikan kecuali ada perjanjian khusus.
        </div>
    <?php endif; ?>

    <div class="no-print" style="text-align: center; margin-top: 20px;">
        <button onclick="window.print()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
            <i class="fas fa-print"></i> Print
        </button>
        <button onclick="window.close()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">
            Close
        </button>
    </div>

    <script>
        // Auto print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>

</html>