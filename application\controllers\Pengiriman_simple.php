<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Simple Pengiriman Controller for Testing
 * Simplified version without complex access control
 */
class Pengiriman_simple extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Mod_pengiriman');
        $this->load->library('form_validation');
        $this->load->database();
    }

    // Simple form input without access control
    public function form_input()
    {
        // Set content type
        header('Content-Type: text/html; charset=utf-8');
        
        try {
            // Initialize data array with defaults
            $data = array(
                'pesanan_list' => array(),
                'pelanggan_list' => array(),
                'nomor_pengiriman' => 'PGR-' . date('Ymd') . '-0001'
            );
            
            // Try to load data from model
            try {
                $data['pesanan_list'] = $this->Mod_pengiriman->get_pesanan_for_shipping();
                if (!is_array($data['pesanan_list'])) {
                    $data['pesanan_list'] = array();
                }
            } catch (Exception $e) {
                log_message('error', 'Error loading pesanan: ' . $e->getMessage());
                $data['pesanan_list'] = array();
            }
            
            try {
                $data['pelanggan_list'] = $this->Mod_pengiriman->get_pelanggan_dropdown();
                if (!is_array($data['pelanggan_list'])) {
                    $data['pelanggan_list'] = array();
                }
            } catch (Exception $e) {
                log_message('error', 'Error loading pelanggan: ' . $e->getMessage());
                $data['pelanggan_list'] = array();
            }
            
            try {
                $nomor = $this->Mod_pengiriman->generate_nomor_pengiriman();
                if (!empty($nomor)) {
                    $data['nomor_pengiriman'] = $nomor;
                }
            } catch (Exception $e) {
                log_message('error', 'Error generating nomor: ' . $e->getMessage());
                // Keep default nomor
            }
            
            // Load the view
            $this->load->view('pengiriman/form_input', $data);
            
        } catch (Exception $e) {
            // Show error message
            echo '<div class="alert alert-danger">
                    <h5><i class="fa fa-exclamation-triangle"></i> Error Loading Form</h5>
                    <p><strong>Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>
                    <hr>
                    <p><strong>Debug Info:</strong></p>
                    <ul>
                        <li>Controller: Pengiriman_simple</li>
                        <li>Method: form_input</li>
                        <li>Time: ' . date('Y-m-d H:i:s') . '</li>
                    </ul>
                    <p><strong>Possible Solutions:</strong></p>
                    <ul>
                        <li>Check database connection</li>
                        <li>Verify tables exist (run pengiriman_setup.sql)</li>
                        <li>Check model file exists</li>
                        <li>Check view file exists</li>
                    </ul>
                  </div>';
        }
    }

    // Test method to check if controller works
    public function test()
    {
        echo "<h2>Pengiriman Simple Controller Test</h2>";
        echo "<p>Controller loaded successfully!</p>";
        echo "<p>Time: " . date('Y-m-d H:i:s') . "</p>";
        
        // Test database
        try {
            $query = $this->db->query("SELECT 1 as test");
            echo "<p style='color: green;'>✓ Database connection OK</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
        }
        
        // Test model
        try {
            $this->load->model('Mod_pengiriman');
            echo "<p style='color: green;'>✓ Model loaded OK</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Model error: " . $e->getMessage() . "</p>";
        }
        
        // Test view
        try {
            $data = array(
                'pesanan_list' => array(),
                'pelanggan_list' => array(),
                'nomor_pengiriman' => 'TEST-001'
            );
            
            ob_start();
            $this->load->view('pengiriman/form_input', $data);
            $view_content = ob_get_clean();
            
            if (strlen($view_content) > 100) {
                echo "<p style='color: green;'>✓ View loaded OK (" . strlen($view_content) . " chars)</p>";
            } else {
                echo "<p style='color: orange;'>⚠ View loaded but content seems short</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ View error: " . $e->getMessage() . "</p>";
        }
        
        echo "<hr>";
        echo "<p><a href='" . site_url('pengiriman_simple/form_input') . "'>Test Form Input</a></p>";
        echo "<p><a href='" . site_url('pengiriman_simple/test_ajax') . "'>Test AJAX Methods</a></p>";
    }

    // Test AJAX methods
    public function test_ajax()
    {
        echo "<h2>AJAX Methods Test</h2>";
        
        // Test generate nomor
        try {
            $nomor = $this->Mod_pengiriman->generate_nomor_pengiriman();
            echo "<p>✓ Generate nomor: $nomor</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Generate nomor error: " . $e->getMessage() . "</p>";
        }
        
        // Test get pesanan
        try {
            $pesanan = $this->Mod_pengiriman->get_pesanan_for_shipping();
            echo "<p>✓ Get pesanan: " . count($pesanan) . " records</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Get pesanan error: " . $e->getMessage() . "</p>";
        }
        
        // Test get pelanggan
        try {
            $pelanggan = $this->Mod_pengiriman->get_pelanggan_dropdown();
            echo "<p>✓ Get pelanggan: " . count($pelanggan) . " records</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Get pelanggan error: " . $e->getMessage() . "</p>";
        }
    }

    // Simple generate nomor for AJAX
    public function generate_nomor()
    {
        try {
            $nomor = $this->Mod_pengiriman->generate_nomor_pengiriman();
            echo json_encode(array('status' => true, 'nomor' => $nomor));
        } catch (Exception $e) {
            echo json_encode(array('status' => false, 'error' => $e->getMessage()));
        }
    }

    // Simple get pesanan detail for AJAX
    public function get_pesanan_detail()
    {
        try {
            $id_pesanan = $this->input->post('id_pesanan');
            if ($id_pesanan) {
                $detail = $this->Mod_pengiriman->get_pesanan_detail($id_pesanan);
                echo json_encode($detail);
            } else {
                echo json_encode(array());
            }
        } catch (Exception $e) {
            echo json_encode(array('error' => $e->getMessage()));
        }
    }

    // Simple get gudang with stock for AJAX
    public function get_gudang_with_stock()
    {
        try {
            $id_barang = $this->input->post('id_barang');
            if ($id_barang) {
                $gudang = $this->Mod_pengiriman->get_gudang_with_stock($id_barang);
                echo json_encode($gudang);
            } else {
                echo json_encode(array());
            }
        } catch (Exception $e) {
            echo json_encode(array('error' => $e->getMessage()));
        }
    }

    // Simple insert method
    public function insert()
    {
        try {
            // Basic validation
            if (empty($this->input->post('tanggal_pengiriman'))) {
                echo json_encode(array('status' => false, 'message' => 'Tanggal pengiriman harus diisi'));
                return;
            }

            $nomor = $this->input->post('nomor_pengiriman');
            if (empty($nomor)) {
                $nomor = $this->Mod_pengiriman->generate_nomor_pengiriman();
            }

            $save = array(
                'nomor_pengiriman' => $nomor,
                'tanggal_pengiriman' => $this->input->post('tanggal_pengiriman'),
                'id_pesanan' => $this->input->post('id_pesanan') ?: null,
                'id_pelanggan' => $this->input->post('id_pelanggan') ?: null,
                'alamat_pengiriman' => $this->input->post('alamat_pengiriman') ?: '',
                'ekspedisi' => $this->input->post('ekspedisi') ?: null,
                'nomor_resi' => $this->input->post('nomor_resi') ?: null,
                'biaya_pengiriman' => $this->input->post('biaya_pengiriman') ?: 0,
                'status' => $this->input->post('status') ?: 'draft',
                'estimasi_tiba' => $this->input->post('estimasi_tiba') ?: null,
                'keterangan' => $this->input->post('keterangan') ?: null,
                'created_by' => 'test_user',
            );

            $id_pengiriman = $this->Mod_pengiriman->insert($save);

            echo json_encode(array(
                'status' => true,
                'id_pengiriman' => $id_pengiriman,
                'message' => 'Data berhasil disimpan'
            ));

        } catch (Exception $e) {
            echo json_encode(array(
                'status' => false,
                'message' => 'Error: ' . $e->getMessage()
            ));
        }
    }
}
