<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Controller Stok Opname
 * Mengatur stok opname dan detailnya
 */
class StokOpname extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model(array('Mod_stok_opname', 'Mod_dashboard'));
        $this->load->library('form_validation');
    }

    public function index()
    {
        $link = $this->uri->segment(1);
        $level = $this->session->userdata('id_level');

        // Cek Posisi Menu apakah Sub Menu Atau bukan
        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        
        if ($jml > 0) { //Jika Menu
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $a_menu = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $a_menu->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $a_submenu = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $a_submenu->view ?? 'N';
        }

        if ($akses == "Y") {
            $this->template->load('layoutbackend', 'stok_opname/stok_opname', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        ini_set('memory_limit', '512M');
        set_time_limit(3600);
        $list = $this->Mod_stok_opname->get_datatables();
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $opname) {
            $no++;
            $row = array();
            $row[] = $opname->nomor_opname;
            $row[] = date('d/m/Y', strtotime($opname->tanggal_opname));
            $row[] = $opname->nama_gudang . '<br><small class="text-muted">' . $opname->kode_gudang . '</small>';
            
            // Status dengan badge
            $status_class = $opname->status == 'final' ? 'badge-success' : 'badge-warning';
            $row[] = '<span class="badge ' . $status_class . '">' . strtoupper($opname->status) . '</span>';
            
            $row[] = number_format($opname->total_item, 0);
            
            // Selisih positif (lebih)
            $row[] = '<span class="text-success">+' . number_format($opname->total_selisih_positif, 0) . '</span>';

            // Selisih negatif (kurang)
            $row[] = '<span class="text-danger">-' . number_format($opname->total_selisih_negatif, 0) . '</span>';

            // Action buttons
            $actions = '';
            if ($opname->status == 'draft') {
                $actions .= '<a class="btn btn-xs btn-outline-info edit" href="javascript:void(0)" title="Edit" onclick="edit(' . $opname->id . ')"><i class="fas fa-edit"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $opname->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-success finalize" href="javascript:void(0)" title="Finalisasi" onclick="finalize(' . $opname->id . ')"><i class="fas fa-check"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-danger delete" href="javascript:void(0)" title="Delete" onclick="hapus(' . $opname->id . ')"><i class="fas fa-trash"></i></a>';
            } else {
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $opname->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-info print" href="javascript:void(0)" title="Print" onclick="print_opname(' . $opname->id . ')"><i class="fas fa-print"></i></a>';
            }
            
            $row[] = $actions;
            $data[] = $row;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_stok_opname->count_all(),
            "recordsFiltered" => $this->Mod_stok_opname->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function insert()
    {
        $this->_validate();

        // Generate nomor otomatis jika tidak diisi
        $nomor = $this->input->post('nomor_opname');
        if (empty($nomor)) {
            $nomor = $this->Mod_stok_opname->generate_nomor();
        }

        $save = array(
            'nomor_opname' => $nomor,
            'tanggal_opname' => $this->input->post('tanggal_opname'),
            'id_gudang' => $this->input->post('id_gudang'),
            'keterangan' => $this->input->post('keterangan'),
            'user_input' => $this->session->userdata('username'),
        );
        
        $id_opname = $this->Mod_stok_opname->insert($save);
        
        // Auto generate detail jika diminta
        if ($this->input->post('auto_generate') == '1') {
            $id_gudang = $this->input->post('id_gudang');
            $total_generated = $this->Mod_stok_opname->auto_generate_detail($id_opname, $id_gudang);
        }
        
        echo json_encode(array(
            "status" => TRUE,
            "id_opname" => $id_opname,
            "total_generated" => isset($total_generated) ? $total_generated : 0
        ));
    }

    public function update()
    {
        $this->_validate();

        $id = $this->input->post('id');

        $save = array(
            'tanggal_opname' => $this->input->post('tanggal_opname'),
            'id_gudang' => $this->input->post('id_gudang'),
            'keterangan' => $this->input->post('keterangan'),
        );

        $this->Mod_stok_opname->update($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit($id)
    {
        $data = $this->Mod_stok_opname->get($id);
        echo json_encode($data);
    }

    public function form_input()
    {
        // Load dropdown data untuk form
        $data['gudang_list'] = $this->Mod_stok_opname->get_gudang_dropdown();
        $this->load->view('stok_opname/form_input', $data);
    }

    public function delete()
    {
        $id = $this->input->post('id');
        $this->Mod_stok_opname->delete($id);
        echo json_encode(array("status" => TRUE));
    }

    public function generate_nomor()
    {
        $nomor = $this->Mod_stok_opname->generate_nomor();
        echo json_encode(array('nomor' => $nomor));
    }

    // Finalisasi opname
    public function finalize()
    {
        $id = $this->input->post('id');
        $user_final = $this->session->userdata('username');
        
        $result = $this->Mod_stok_opname->finalize_opname($id, $user_final);
        
        if ($result) {
            echo json_encode(array("status" => TRUE, "message" => "Opname berhasil difinalisasi"));
        } else {
            echo json_encode(array("status" => FALSE, "message" => "Gagal finalisasi opname"));
        }
    }

    // ===== DETAIL OPNAME METHODS =====

    public function detail($id)
    {
        $data['opname'] = $this->Mod_stok_opname->get($id);
        $data['detail_list'] = $this->Mod_stok_opname->get_detail($id);
        $data['barang_available'] = $this->Mod_stok_opname->get_barang_available($id);
        $this->load->view('stok_opname/detail_opname', $data);
    }

    /**
     * Method untuk mendapatkan hanya bagian tabel detail
     * Digunakan untuk reload tabel tanpa reload seluruh modal
     */
    public function get_detail_table($id)
    {
        $data['opname'] = $this->Mod_stok_opname->get($id);
        $data['detail_list'] = $this->Mod_stok_opname->get_detail($id);
        $this->load->view('stok_opname/detail_table_only', $data);
    }

    public function ajax_detail_list()
    {
        $id_opname = $this->input->post('id_opname');
        $detail_list = $this->Mod_stok_opname->get_detail($id_opname);
        
        $data = array();
        $no = 1;
        foreach ($detail_list as $detail) {
            $row = array();
            $row[] = $no++;
            $row[] = $detail->kode_barang;
            $row[] = $detail->nama_barang;
            $row[] = number_format($detail->qty_sistem, 0);
            $row[] = number_format($detail->qty_fisik, 0);

            // Selisih dengan warna
            $selisih_class = $detail->selisih >= 0 ? 'text-success' : 'text-danger';
            $selisih_icon = $detail->selisih >= 0 ? '+' : '';
            $row[] = '<span class="' . $selisih_class . '">' . $selisih_icon . number_format($detail->selisih, 0) . '</span>';
            
            $row[] = $detail->keterangan ?: '-';
            
            // Action untuk detail
            $actions = '<a class="btn btn-xs btn-outline-info edit-detail" href="javascript:void(0)" title="Edit" onclick="editDetail(' . $detail->id . ')"><i class="fas fa-edit"></i></a> ';
            $actions .= '<a class="btn btn-xs btn-outline-danger delete-detail" href="javascript:void(0)" title="Delete" onclick="deleteDetail(' . $detail->id . ')"><i class="fas fa-trash"></i></a>';
            $row[] = $actions;
            
            $data[] = $row;
        }
        
        echo json_encode(array("data" => $data));
    }

    public function insert_detail()
    {
        $this->_validate_detail();

        $id_opname = $this->input->post('id_opname');
        $id_barang = $this->input->post('id_barang');
        $id_gudang = $this->input->post('id_gudang');

        // Get qty sistem dari stok_barang
        $qty_sistem = $this->Mod_stok_opname->get_stok_sistem_barang($id_barang, $id_gudang);

        $save = array(
            'id_opname' => $id_opname,
            'id_barang' => $id_barang,
            'qty_sistem' => $qty_sistem,
            'qty_fisik' => $this->input->post('qty_fisik'),
            'keterangan' => $this->input->post('keterangan'),
        );

        $this->Mod_stok_opname->insert_detail($save);
        echo json_encode(array("status" => TRUE));
    }

    public function update_detail()
    {
        $this->_validate_detail();

        $id = $this->input->post('id');

        $save = array(
            'qty_fisik' => $this->input->post('qty_fisik'),
            'keterangan' => $this->input->post('keterangan'),
        );

        $this->Mod_stok_opname->update_detail($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit_detail($id)
    {
        $data = $this->Mod_stok_opname->get_detail_by_id($id);
        echo json_encode($data);
    }

    public function delete_detail()
    {
        $id = $this->input->post('id');
        $this->Mod_stok_opname->delete_detail($id);
        echo json_encode(array("status" => TRUE));
    }

    public function auto_generate_detail()
    {
        $id_opname = $this->input->post('id_opname');
        $id_gudang = $this->input->post('id_gudang');

        $total_generated = $this->Mod_stok_opname->auto_generate_detail($id_opname, $id_gudang);

        echo json_encode(array(
            "status" => TRUE,
            "total_generated" => $total_generated,
            "message" => "Berhasil generate " . $total_generated . " item dari stok sistem"
        ));
    }

    public function get_stok_sistem()
    {
        $id_barang = $this->input->post('id_barang');
        $id_gudang = $this->input->post('id_gudang');

        $qty_sistem = $this->Mod_stok_opname->get_stok_sistem_barang($id_barang, $id_gudang);

        echo json_encode(array('qty_sistem' => $qty_sistem));
    }

    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        $nomor = $this->input->post('nomor_opname');
        $id = $this->input->post('id');

        // Validasi nomor opname
        if (empty($nomor)) {
            // Nomor boleh kosong, akan di-generate otomatis
        } else {
            if (!preg_match('/^SO-[0-9]{4}-[0-9]{3}$/', $nomor)) {
                $data['inputerror'][] = 'nomor_opname';
                $data['error_string'][] = 'Format nomor harus SO-2025-001, SO-2025-002, dst.';
                $data['status'] = FALSE;
            } else if ($this->Mod_stok_opname->check_nomor_exists($nomor, $id)) {
                $data['inputerror'][] = 'nomor_opname';
                $data['error_string'][] = 'Nomor opname sudah ada';
                $data['status'] = FALSE;
            }
        }

        // Validasi tanggal
        if (empty($this->input->post('tanggal_opname'))) {
            $data['inputerror'][] = 'tanggal_opname';
            $data['error_string'][] = 'Tanggal opname wajib diisi';
            $data['status'] = FALSE;
        }

        // Validasi gudang
        if (empty($this->input->post('id_gudang'))) {
            $data['inputerror'][] = 'id_gudang';
            $data['error_string'][] = 'Gudang wajib dipilih';
            $data['status'] = FALSE;
        }

        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }

    private function _validate_detail()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        // Validasi barang (untuk insert baru)
        if ($this->input->post('id_barang') && empty($this->input->post('id_barang'))) {
            $data['inputerror'][] = 'id_barang';
            $data['error_string'][] = 'Barang wajib dipilih';
            $data['status'] = FALSE;
        }

        // Validasi qty fisik
        $qty_fisik = $this->input->post('qty_fisik');
        if (empty($qty_fisik) && $qty_fisik !== '0') {
            $data['inputerror'][] = 'qty_fisik';
            $data['error_string'][] = 'Qty fisik wajib diisi';
            $data['status'] = FALSE;
        } else if (!is_numeric($qty_fisik) || $qty_fisik < 0) {
            $data['inputerror'][] = 'qty_fisik';
            $data['error_string'][] = 'Qty fisik harus berupa angka positif';
            $data['status'] = FALSE;
        }

        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }

    public function cetak_opname($id)
    {
        // Load data untuk cetak
        $data['opname'] = $this->Mod_stok_opname->get($id);
        $data['detail_list'] = $this->Mod_stok_opname->get_detail($id);
        
        // Load view cetak
        $this->load->view('stok_opname/cetak_opname', $data);
    }
}
