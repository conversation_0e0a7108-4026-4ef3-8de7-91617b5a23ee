<!-- Header Info -->
<div class="row mb-3">
    <div class="col-md-6">
        <table class="table table-sm table-borderless">
            <tr>
                <td width="140"><strong><PERSON><PERSON></strong></td>
                <td width="10">:</td>
                <td><?= $barang_masuk->nomor_penerimaan ?></td>
            </tr>
            <tr>
                <td><strong>Tanggal</strong></td>
                <td>:</td>
                <td><?= date('d/m/Y', strtotime($barang_masuk->tanggal)) ?></td>
            </tr>
            <tr>
                <td><strong>Jenis</strong></td>
                <td>:</td>
                <td>
                    <?php
                    $jenis_class = 'badge-primary';
                    switch ($barang_masuk->jenis) {
                        case 'pembelian':
                            $jenis_class = 'badge-primary';
                            break;
                        case 'retur_penjualan':
                            $jenis_class = 'badge-info';
                            break;
                        case 'bonus':
                            $jenis_class = 'badge-success';
                            break;
                        case 'transfer_masuk':
                            $jenis_class = 'badge-warning';
                            break;
                        default:
                            $jenis_class = 'badge-secondary';
                            break;
                    }
                    ?>
                    <span class="badge <?= $jenis_class ?>"><?= strtoupper(str_replace('_', ' ', $barang_masuk->jenis)) ?></span>
                </td>
            </tr>
            <tr>
                <td><strong>Supplier</strong></td>
                <td>:</td>
                <td><?= $barang_masuk->nama_supplier ? $barang_masuk->nama_supplier . ' (' . $barang_masuk->kode_supplier . ')' : '-' ?></td>
            </tr>
        </table>
    </div>
    <div class="col-md-6">
        <table class="table table-sm table-borderless">
            <tr>
                <td width="120"><strong>Status</strong></td>
                <td width="10">:</td>
                <td>
                    <?php if ($barang_masuk->status == 'final'): ?>
                        <span class="badge badge-success">FINAL</span>
                    <?php else: ?>
                        <span class="badge badge-warning">DRAFT</span>
                    <?php endif; ?>
                </td>
            </tr>
            <tr>
                <td><strong>Total Item</strong></td>
                <td>:</td>
                <td><?= number_format($barang_masuk->total_item, 0) ?> item</td>
            </tr>
            <tr>
                <td><strong>Total Qty</strong></td>
                <td>:</td>
                <td><?= number_format($barang_masuk->total_qty, 0) ?></td>
            </tr>
            <tr>
                <td><strong>Ref. Nomor</strong></td>
                <td>:</td>
                <td><?= $barang_masuk->ref_nomor ?: '-' ?></td>
            </tr>
        </table>
    </div>
</div>

<?php if (!empty($barang_masuk->keterangan)): ?>
    <div class="row mb-3">
        <div class="col-12">
            <div class="alert alert-info">
                <strong>Keterangan:</strong> <?= $barang_masuk->keterangan ?>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Action Buttons -->
<?php if ($barang_masuk->status == 'draft'): ?>
    <div class="row mb-3">
        <div class="col-12">
            <button type="button" class="btn btn-sm btn-outline-primary" onclick="addDetailItem()">
                <i class="fas fa-plus"></i> Tambah Item
            </button>
            <button type="button" class="btn btn-sm btn-outline-success" onclick="finalizeFromDetail()">
                <i class="fas fa-check"></i> Finalisasi Barang Masuk
            </button>
        </div>
    </div>
<?php endif; ?>

<!-- Detail Table -->
<div class="table-responsive">
    <table id="tbl_detail_barang_masuk" class="table table-bordered table-striped table-sm">
        <thead class="bg-light">
            <tr>
                <th width="5%">No</th>
                <th width="15%">Kode Barang</th>
                <th width="25%">Nama Barang</th>
                <th width="15%">Gudang</th>
                <th width="10%">Qty</th>
                <th width="10%">Satuan</th>
                <th width="12%">Harga Satuan</th>
                <th width="12%">Total Harga</th>
                <?php if ($barang_masuk->status == 'draft'): ?>
                    <th width="10%">Aksi</th>
                <?php endif; ?>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($detail_list)): ?>
                <?php $no = 1;
                $total_nilai = 0;
                foreach ($detail_list as $detail): ?>
                    <tr>
                        <td><?= $no++ ?></td>
                        <td><?= $detail->kode_barang ?></td>
                        <td>
                            <?= $detail->nama_barang ?>
                            <?php if ($detail->merk || $detail->tipe): ?>
                                <br><small class="text-muted"><?= trim($detail->merk . ' ' . $detail->tipe) ?></small>
                            <?php endif; ?>
                        </td>
                        <td><?= $detail->nama_gudang ?><br><small class="text-muted"><?= $detail->kode_gudang ?></small></td>
                        <td class="text-right"><?= number_format($detail->qty_diterima, 0) ?></td>
                        <td><?= $detail->nama_satuan ?: '-' ?></td>
                        <td class="text-right"><?= number_format($detail->harga_satuan, 0) ?></td>
                        <td class="text-right"><?= number_format($detail->total_harga, 0) ?></td>
                        <?php if ($barang_masuk->status == 'draft'): ?>
                            <td>
                                <button type="button" class="btn btn-xs btn-outline-info" onclick="editDetailItem(<?= $detail->id ?>)" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-xs btn-outline-danger" onclick="deleteDetailItem(<?= $detail->id ?>)" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        <?php endif; ?>
                    </tr>
                    <?php $total_nilai += $detail->total_harga; ?>
                <?php endforeach; ?>

                <!-- Total Row -->
                <tr class="bg-light font-weight-bold">
                    <td colspan="<?= $barang_masuk->status == 'draft' ? '7' : '6' ?>">TOTAL</td>
                    <td class="text-right"><?= number_format($total_nilai, 0) ?></td>
                    <?php if ($barang_masuk->status == 'draft'): ?>
                        <td></td>
                    <?php endif; ?>
                </tr>
            <?php else: ?>
                <tr>
                    <td colspan="<?= $barang_masuk->status == 'draft' ? '9' : '8' ?>" class="text-center">
                        Belum ada detail item.
                        <?php if ($barang_masuk->status == 'draft'): ?>
                            <a href="javascript:void(0)" onclick="addDetailItem()">Klik di sini untuk menambah item</a>
                        <?php endif; ?>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<!-- Modal Form Detail -->
<div class="modal fade" id="modal_form_detail" role="dialog" style="z-index: 1060;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Detail Item Barang Masuk</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="form_detail">
                    <input type="hidden" name="id" id="detail_id">
                    <input type="hidden" name="id_barang_masuk" value="<?= $barang_masuk->id ?>">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group" id="barang_group">
                                <label for="id_barang">Barang <span class="text-danger">*</span></label>
                                <select class="form-control select2" name="id_barang" id="id_barang" required style="width: 100%;">
                                    <option value="">-- Pilih Barang --</option>
                                    <?php foreach ($barang_list as $barang): ?>
                                        <option value="<?= $barang->id ?>" data-satuan="<?= $barang->satuan_id ?>" data-harga="<?= $barang->harga_beli ?>"><?= $barang->kode_barang ?> - <?= $barang->nama_barang ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <span class="help-block"></span>
                            </div>

                            <div class="form-group">
                                <label for="id_gudang">Gudang <span class="text-danger">*</span></label>
                                <select class="form-control select2" name="id_gudang" id="id_gudang" required style="width: 100%;">
                                    <option value="">-- Pilih Gudang --</option>
                                    <?php foreach ($gudang_list as $gudang): ?>
                                        <option value="<?= $gudang->id ?>"><?= $gudang->kode_gudang ?> - <?= $gudang->nama_gudang ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <span class="help-block"></span>
                            </div>

                            <div class="form-group">
                                <label for="id_satuan">Satuan</label>
                                <select class="form-control select2" name="id_satuan" id="id_satuan" style="width: 100%;">
                                    <option value="">-- Pilih Satuan --</option>
                                    <?php foreach ($satuan_list as $satuan): ?>
                                        <option value="<?= $satuan->id ?>"><?= $satuan->kode_satuan ?> - <?= $satuan->nama_satuan ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <span class="help-block"></span>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="qty_diterima">Qty Diterima <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" name="qty_diterima" id="qty_diterima" required min="0" step="0.01" onchange="updateTotalHarga()">
                                <span class="help-block"></span>
                            </div>

                            <div class="form-group">
                                <label for="harga_satuan">Harga Satuan</label>
                                <input type="number" class="form-control" name="harga_satuan" id="harga_satuan" min="0" step="0.01" onchange="updateTotalHarga()">
                                <small class="form-text text-muted">Opsional. Untuk keperluan perhitungan nilai.</small>
                                <span class="help-block"></span>
                            </div>

                            <div class="form-group">
                                <label>Total Harga</label>
                                <div class="form-control-plaintext">
                                    <strong id="total_harga_display">0</strong>
                                    <small class="text-muted ml-2">(Qty × Harga Satuan)</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="keterangan_detail">Keterangan</label>
                        <textarea class="form-control" name="keterangan" id="keterangan_detail" rows="2" placeholder="Keterangan untuk item ini (opsional)"></textarea>
                        <span class="help-block"></span>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSaveDetail" onclick="saveDetail()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<style>
    /* Ensure proper z-index layering for nested modals */
    #modal_form_detail {
        z-index: 1060 !important;
    }

    #modal_form_detail .modal-backdrop {
        z-index: 1055 !important;
    }
</style>

<script>
    var save_method_detail;
    var current_barang_masuk_id = <?= $barang_masuk->id ?>;

    $(document).ready(function() {
        // Initialize Select2 untuk modal detail
        $('#modal_form_detail').on('shown.bs.modal', function() {
            $('.select2').select2({
                dropdownParent: $('#modal_form_detail'),
                placeholder: "-- Pilih --",
                allowClear: true
            });
        });

        // Prevent parent modal from closing when child modal is active
        $('#modal_form_detail').on('show.bs.modal', function(e) {
            // Set z-index yang lebih tinggi untuk modal child
            $(this).css('z-index', 1060);
            // Disable backdrop click pada modal parent sementara
            $('#modal_detail').off('click.bs.modal.data-api');
        });

        // Restore parent modal behavior when child modal closes
        $('#modal_form_detail').on('hidden.bs.modal', function(e) {
            // Reset z-index
            $(this).css('z-index', '');
            // Re-enable backdrop click pada modal parent
            $('#modal_detail').on('click.bs.modal.data-api', '[data-dismiss="modal"]', function(e) {
                $('#modal_detail').modal('hide');
            });
        });

        // Event handler untuk perubahan barang
        $('#id_barang').on('change', function() {
            var selectedOption = $(this).find('option:selected');
            var satuanId = selectedOption.data('satuan');
            var hargaBeli = selectedOption.data('harga');

            if (satuanId) {
                $('#id_satuan').val(satuanId).trigger('change');
            }

            if (hargaBeli) {
                $('#harga_satuan').val(hargaBeli);
                updateTotalHarga();
            }
        });
    });

    function addDetailItem() {
        save_method_detail = 'add';
        $('#form_detail')[0].reset();
        $('#detail_id').val('');
        $('#barang_group').show();
        $('#total_harga_display').text('0');
        $('#modal_form_detail .modal-title').text('Tambah Detail Item');

        // Tampilkan modal child
        $('#modal_form_detail').modal('show');
    }

    function editDetailItem(id) {
        save_method_detail = 'update';
        $('#barang_group').show();
        $('.modal-title').text('Edit Detail Item');

        $.ajax({
            url: "<?php echo site_url('BarangMasuk/edit_detail/') ?>" + id,
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                $('#detail_id').val(data.id);
                $('#id_barang').val(data.id_barang).trigger('change');
                $('#id_gudang').val(data.id_gudang).trigger('change');
                $('#id_satuan').val(data.id_satuan).trigger('change');
                $('#qty_diterima').val(data.qty_diterima);
                $('#harga_satuan').val(data.harga_satuan);
                $('#keterangan_detail').val(data.keterangan);
                updateTotalHarga();
                // Tampilkan modal child
                $('#modal_form_detail').modal('show');
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat data detail.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function saveDetail() {
        $('#btnSaveDetail').text('saving...').prop('disabled', true);

        var url;
        if (save_method_detail == 'add') {
            url = "<?php echo site_url('BarangMasuk/insert_detail') ?>";
        } else {
            url = "<?php echo site_url('BarangMasuk/update_detail') ?>";
        }

        $.ajax({
            url: url,
            type: "POST",
            data: $('#form_detail').serialize(),
            dataType: "JSON",
            success: function(data) {
                if (data.status) {
                    $('#modal_form_detail').modal('hide');

                    // Tampilkan pesan sukses
                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Detail item berhasil disimpan.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(function() {
                        // Refresh tabel detail tanpa reload halaman
                        refreshDetailTable();
                        // Refresh tabel parent juga
                        refreshParentTable();
                    });
                } else {
                    for (var i = 0; i < data.inputerror.length; i++) {
                        $('[name="' + data.inputerror[i] + '"]').addClass('is-invalid');
                        $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]);
                    }

                    var errorMessages = data.error_string.join('<br>');
                    Swal.fire({
                        title: 'Gagal Menyimpan!',
                        html: 'Terjadi kesalahan:<br>' + errorMessages,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menyimpan detail.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            },
            complete: function() {
                $('#btnSaveDetail').text('Save').prop('disabled', false);
            }
        });
    }

    function deleteDetailItem(id) {
        Swal.fire({
            title: 'Apakah Anda yakin?',
            text: "Detail item akan dihapus!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Ya, hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: "<?php echo site_url('BarangMasuk/delete_detail') ?>",
                    type: "POST",
                    dataType: "JSON",
                    data: {
                        id: id
                    },
                    success: function(data) {
                        if (data.status) {
                            // Tampilkan pesan sukses
                            Swal.fire({
                                title: 'Terhapus!',
                                text: 'Detail item berhasil dihapus.',
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then(function() {
                                // Refresh tabel detail tanpa reload halaman
                                refreshDetailTable();
                                // Refresh tabel parent juga
                                refreshParentTable();
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: 'Gagal menghapus detail item.',
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat menghapus detail.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }

    function finalizeFromDetail() {
        Swal.fire({
            title: 'Finalisasi Barang Masuk?',
            text: "Setelah difinalisasi, data tidak dapat diubah lagi dan akan otomatis update stok sistem!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Ya, finalisasi!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: "<?php echo site_url('BarangMasuk/finalize') ?>",
                    type: "POST",
                    dataType: "JSON",
                    data: {
                        id: current_barang_masuk_id
                    },
                    success: function(data) {
                        if (data.status) {
                            // Tampilkan pesan sukses
                            Swal.fire({
                                title: 'Berhasil!',
                                text: 'Barang masuk berhasil difinalisasi dan stok sistem telah diupdate.',
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then(function() {
                                // Tutup modal detail dan kembali ke halaman utama
                                $('#modal_detail').modal('hide');
                                // Refresh tabel utama barang masuk
                                refreshParentTable();
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: data.message || 'Gagal finalisasi barang masuk.',
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat finalisasi.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }

    function updateTotalHarga() {
        var qty = parseFloat($('#qty_diterima').val()) || 0;
        var harga = parseFloat($('#harga_satuan').val()) || 0;
        var total = qty * harga;

        $('#total_harga_display').text(total.toLocaleString('id-ID'));
    }

    // Fungsi untuk refresh tabel detail tanpa reload halaman
    function refreshDetailTable() {
        // Reload konten tabel detail
        $.ajax({
            url: "<?php echo site_url('BarangMasuk/get_detail_items/') . $barang_masuk->id ?>",
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                // Update tabel detail dengan ID yang benar
                var tableBody = $('#tbl_detail_barang_masuk tbody');
                tableBody.empty();

                var totalHarga = 0;
                var isDraft = <?= $barang_masuk->status == 'draft' ? 'true' : 'false' ?>;

                if (data.length > 0) {
                    $.each(data, function(index, item) {
                        var subtotal = parseFloat(item.total_harga || (item.qty_diterima * item.harga_satuan));
                        totalHarga += subtotal;

                        var merkTipe = '';
                        if (item.merk || item.tipe) {
                            merkTipe = '<br><small class="text-muted">' + (item.merk + ' ' + item.tipe).trim() + '</small>';
                        }

                        var row = '<tr>' +
                            '<td>' + (index + 1) + '</td>' +
                            '<td>' + item.kode_barang + '</td>' +
                            '<td>' + item.nama_barang + merkTipe + '</td>' +
                            '<td>' + item.nama_gudang + '<br><small class="text-muted">' + item.kode_gudang + '</small></td>' +
                            '<td class="text-right">' + parseInt(item.qty_diterima).toLocaleString() + '</td>' +
                            '<td>' + (item.nama_satuan || '-') + '</td>' +
                            '<td class="text-right">' + parseInt(item.harga_satuan).toLocaleString() + '</td>' +
                            '<td class="text-right">' + parseInt(subtotal).toLocaleString() + '</td>';

                        if (isDraft) {
                            row += '<td>' +
                                '<button type="button" class="btn btn-xs btn-outline-info" onclick="editDetailItem(' + item.id + ')" title="Edit">' +
                                '<i class="fas fa-edit"></i>' +
                                '</button> ' +
                                '<button type="button" class="btn btn-xs btn-outline-danger" onclick="deleteDetailItem(' + item.id + ')" title="Delete">' +
                                '<i class="fas fa-trash"></i>' +
                                '</button>' +
                                '</td>';
                        }

                        row += '</tr>';
                        tableBody.append(row);
                    });

                    // Tambahkan baris total
                    var totalRow = '<tr class="bg-light font-weight-bold">' +
                        '<td colspan="' + (isDraft ? '7' : '6') + '">TOTAL</td>' +
                        '<td class="text-right">' + parseInt(totalHarga).toLocaleString() + '</td>';

                    if (isDraft) {
                        totalRow += '<td></td>';
                    }

                    totalRow += '</tr>';
                    tableBody.append(totalRow);
                } else {
                    var emptyRow = '<tr>' +
                        '<td colspan="' + (isDraft ? '9' : '8') + '" class="text-center">' +
                        'Belum ada detail item.';

                    if (isDraft) {
                        emptyRow += ' <a href="javascript:void(0)" onclick="addDetailItem()">Klik di sini untuk menambah item</a>';
                    }

                    emptyRow += '</td></tr>';
                    tableBody.append(emptyRow);
                }
            },
            error: function() {
                console.log('Error refreshing detail table');
            }
        });
    }

    // Fungsi untuk format rupiah
    function formatRupiah(angka) {
        var number_string = angka.toString().replace(/[^,\d]/g, ''),
            split = number_string.split(','),
            sisa = split[0].length % 3,
            rupiah = split[0].substr(0, sisa),
            ribuan = split[0].substr(sisa).match(/\d{3}/gi);

        if (ribuan) {
            separator = sisa ? '.' : '';
            rupiah += separator + ribuan.join('.');
        }

        rupiah = split[1] != undefined ? rupiah + ',' + split[1] : rupiah;
        return 'Rp ' + rupiah;
    }

    // Fungsi untuk refresh tabel di halaman parent (halaman utama barang masuk)
    function refreshParentTable() {
        // Cek apakah ada tabel DataTable di halaman parent
        if (window.parent && window.parent.table && typeof window.parent.table.ajax !== 'undefined') {
            window.parent.table.ajax.reload(null, false);
        }
        // Alternatif jika modal dibuka di halaman yang sama
        else if (typeof table !== 'undefined' && table.ajax) {
            table.ajax.reload(null, false);
        }
        // Trigger event refresh pada modal parent jika ada
        else if (window.parent && window.parent.$) {
            window.parent.$('#modal_detail').trigger('hidden.bs.modal.refresh');
        }
        // Jika tidak ada DataTable, coba reload halaman parent
        else if (window.parent && window.parent !== window) {
            window.parent.location.reload();
        }
    }
</script>