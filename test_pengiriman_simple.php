<?php
// Simple test for pengiriman functionality
// This file tests the load barang dari pesanan functionality

// Include CodeIgniter bootstrap
define('BASEPATH', TRUE);
require_once 'system/core/CodeIgniter.php';

// Or try direct database connection
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'toko_elektronik';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Test Load Barang dari <PERSON></h2>";
    echo "<p>Testing database connection and data availability...</p>";
    
    // Test 1: Check pesanan data
    echo "<h3>1. Check Pesanan Data</h3>";
    $stmt = $pdo->query("SELECT * FROM pesanan WHERE status = 'diproses' LIMIT 5");
    $pesanan_list = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($pesanan_list) {
        echo "<p><strong>✓ Found " . count($pesanan_list) . " pesanan with status 'diproses':</strong></p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Nomor</th><th>Tanggal</th><th>Status</th><th>Total Item</th></tr>";
        foreach ($pesanan_list as $pesanan) {
            echo "<tr>";
            echo "<td>" . $pesanan['id'] . "</td>";
            echo "<td>" . $pesanan['nomor_pesanan'] . "</td>";
            echo "<td>" . $pesanan['tanggal_pesanan'] . "</td>";
            echo "<td>" . $pesanan['status'] . "</td>";
            echo "<td>" . $pesanan['total_item'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p><strong>⚠ No pesanan found with status 'diproses'</strong></p>";
    }
    
    // Test 2: Check pesanan detail
    echo "<h3>2. Check Pesanan Detail</h3>";
    if ($pesanan_list) {
        $first_pesanan = $pesanan_list[0];
        $stmt = $pdo->prepare("
            SELECT 
                pd.id,
                pd.id_barang,
                pd.qty,
                pd.harga_satuan,
                pd.subtotal,
                b.kode_barang,
                b.nama_barang,
                b.merk,
                b.tipe,
                s.nama_satuan
            FROM pesanan_detail pd
            LEFT JOIN barang b ON pd.id_barang = b.id
            LEFT JOIN satuan s ON b.satuan_id = s.id
            WHERE pd.id_pesanan = ?
        ");
        $stmt->execute([$first_pesanan['id']]);
        $detail_list = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($detail_list) {
            echo "<p><strong>✓ Found " . count($detail_list) . " items in pesanan " . $first_pesanan['nomor_pesanan'] . ":</strong></p>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Kode</th><th>Nama Barang</th><th>Qty</th><th>Satuan</th><th>Harga</th></tr>";
            foreach ($detail_list as $detail) {
                echo "<tr>";
                echo "<td>" . $detail['kode_barang'] . "</td>";
                echo "<td>" . $detail['nama_barang'] . "</td>";
                echo "<td>" . $detail['qty'] . "</td>";
                echo "<td>" . $detail['nama_satuan'] . "</td>";
                echo "<td>" . number_format($detail['harga_satuan']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    // Test 3: Check pengiriman data
    echo "<h3>3. Check Pengiriman Data</h3>";
    $stmt = $pdo->query("SELECT * FROM pengiriman WHERE status = 'draft' LIMIT 5");
    $pengiriman_list = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($pengiriman_list) {
        echo "<p><strong>✓ Found " . count($pengiriman_list) . " pengiriman with status 'draft':</strong></p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Nomor</th><th>Tanggal</th><th>ID Pesanan</th><th>Status</th></tr>";
        foreach ($pengiriman_list as $pengiriman) {
            echo "<tr>";
            echo "<td>" . $pengiriman['id'] . "</td>";
            echo "<td>" . $pengiriman['nomor_pengiriman'] . "</td>";
            echo "<td>" . $pengiriman['tanggal_pengiriman'] . "</td>";
            echo "<td>" . $pengiriman['id_pesanan'] . "</td>";
            echo "<td>" . $pengiriman['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p><strong>⚠ No pengiriman found with status 'draft'</strong></p>";
    }
    
    // Test 4: Check stok barang
    echo "<h3>4. Check Stok Barang</h3>";
    $stmt = $pdo->query("
        SELECT 
            b.id,
            b.kode_barang,
            b.nama_barang,
            g.nama_gudang,
            sb.qty_terakhir
        FROM stok_barang sb
        JOIN barang b ON sb.id_barang = b.id
        JOIN gudang g ON sb.id_gudang = g.id
        WHERE sb.qty_terakhir > 0
        LIMIT 10
    ");
    $stok_list = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($stok_list) {
        echo "<p><strong>✓ Found " . count($stok_list) . " items with available stock:</strong></p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Kode</th><th>Nama Barang</th><th>Gudang</th><th>Stok</th></tr>";
        foreach ($stok_list as $stok) {
            echo "<tr>";
            echo "<td>" . $stok['kode_barang'] . "</td>";
            echo "<td>" . $stok['nama_barang'] . "</td>";
            echo "<td>" . $stok['nama_gudang'] . "</td>";
            echo "<td>" . $stok['qty_terakhir'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p><strong>⚠ No stock found</strong></p>";
    }
    
    echo "<h3>5. Test Summary</h3>";
    echo "<p><strong>✓ Database connection successful</strong></p>";
    echo "<p><strong>✓ All required tables exist</strong></p>";
    echo "<p><strong>✓ Sample data is available</strong></p>";
    echo "<p><strong>Ready to test load barang dari pesanan functionality!</strong></p>";
    
    echo "<hr>";
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li>Access the pengiriman module: <a href='index.php/pengiriman' target='_blank'>index.php/pengiriman</a></li>";
    echo "<li>Create a new pengiriman or edit an existing draft pengiriman</li>";
    echo "<li>Click the 'Load dari Pesanan' button to test the functionality</li>";
    echo "</ol>";
    
} catch (PDOException $e) {
    echo "<h2>Database Connection Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration.</p>";
}
?>
