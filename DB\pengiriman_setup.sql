-- =====================================================
-- PENGIRIMAN MODULE DATABASE SETUP
-- Toko Elektronik - Shipping Module
-- =====================================================

-- Create pengiriman table
CREATE TABLE IF NOT EXISTS `pengiriman` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nomor_pengiriman` varchar(50) NOT NULL,
  `tanggal_pengiriman` date NOT NULL,
  `id_pesanan` int(11) NOT NULL,
  `id_pelanggan` int(11) NOT NULL,
  `alamat_pengiriman` text NOT NULL,
  `ekspedisi` varchar(100) DEFAULT NULL,
  `nomor_resi` varchar(100) DEFAULT NULL,
  `biaya_pengiriman` decimal(15,2) DEFAULT 0.00,
  `status` enum('draft','prepared','shipped','in_transit','delivered','returned','cancelled') NOT NULL DEFAULT 'draft',
  `total_item` int(11) NOT NULL DEFAULT 0,
  `total_qty` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_berat` decimal(10,2) DEFAULT 0.00,
  `estimasi_tiba` date DEFAULT NULL,
  `tanggal_dikirim` datetime DEFAULT NULL,
  `tanggal_diterima` datetime DEFAULT NULL,
  `penerima` varchar(100) DEFAULT NULL,
  `keterangan` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` varchar(50) DEFAULT NULL,
  `updated_by` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nomor_pengiriman` (`nomor_pengiriman`),
  KEY `id_pesanan` (`id_pesanan`),
  KEY `id_pelanggan` (`id_pelanggan`),
  KEY `idx_pengiriman_tanggal` (`tanggal_pengiriman`),
  KEY `idx_pengiriman_status` (`status`),
  CONSTRAINT `pengiriman_ibfk_1` FOREIGN KEY (`id_pesanan`) REFERENCES `pesanan` (`id`) ON DELETE CASCADE,
  CONSTRAINT `pengiriman_ibfk_2` FOREIGN KEY (`id_pelanggan`) REFERENCES `pelanggan` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Create pengiriman_detail table
CREATE TABLE IF NOT EXISTS `pengiriman_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_pengiriman` int(11) NOT NULL,
  `id_barang` int(11) NOT NULL,
  `id_gudang` int(11) NOT NULL,
  `qty_dikirim` decimal(10,2) NOT NULL,
  `berat_satuan` decimal(10,2) DEFAULT 0.00,
  `total_berat` decimal(10,2) GENERATED ALWAYS AS (`qty_dikirim` * `berat_satuan`) STORED,
  `keterangan` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id_pengiriman` (`id_pengiriman`),
  KEY `id_barang` (`id_barang`),
  KEY `id_gudang` (`id_gudang`),
  CONSTRAINT `pengiriman_detail_ibfk_1` FOREIGN KEY (`id_pengiriman`) REFERENCES `pengiriman` (`id`) ON DELETE CASCADE,
  CONSTRAINT `pengiriman_detail_ibfk_2` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`),
  CONSTRAINT `pengiriman_detail_ibfk_3` FOREIGN KEY (`id_gudang`) REFERENCES `gudang` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Insert sample data for testing
INSERT INTO `pengiriman` (`nomor_pengiriman`, `tanggal_pengiriman`, `id_pesanan`, `id_pelanggan`, `alamat_pengiriman`, `ekspedisi`, `nomor_resi`, `biaya_pengiriman`, `status`, `total_item`, `total_qty`, `total_berat`, `estimasi_tiba`, `keterangan`, `created_by`) VALUES
('PGR-20250108-0001', '2025-01-08', 1, 1, 'Jl. Sudirman No. 123, Jakarta Pusat, DKI Jakarta 10110', 'JNE', 'JNE123456789', 25000.00, 'shipped', 2, 3.00, 5.50, '2025-01-10', 'Pengiriman smartphone dan laptop untuk kantor', 'admin'),
('PGR-20250108-0002', '2025-01-08', 2, 2, 'Jl. Gatot Subroto No. 456, Jakarta Selatan, DKI Jakarta 12190', 'TIKI', 'TIKI987654321', 30000.00, 'prepared', 3, 5.00, 8.20, '2025-01-11', 'Pengiriman peralatan elektronik untuk meeting room', 'admin');

-- Insert sample detail data
INSERT INTO `pengiriman_detail` (`id_pengiriman`, `id_barang`, `id_gudang`, `qty_dikirim`, `berat_satuan`, `keterangan`) VALUES
(1, 1, 1, 1.00, 0.20, 'Samsung Galaxy S23 - kondisi baik'),
(1, 2, 1, 2.00, 2.65, 'Laptop ASUS ROG - dalam kemasan original'),
(2, 3, 2, 2.00, 1.50, 'Monitor LG 24 inch - bubble wrap extra'),
(2, 4, 2, 1.00, 0.80, 'Keyboard Logitech - kotak original'),
(2, 5, 2, 2.00, 1.20, 'Mouse Wireless - kemasan retail');

-- Create view for pengiriman summary
CREATE OR REPLACE VIEW `v_pengiriman_summary` AS
SELECT 
    p.id,
    p.nomor_pengiriman,
    p.tanggal_pengiriman,
    p.id_pesanan,
    p.id_pelanggan,
    p.alamat_pengiriman,
    p.ekspedisi,
    p.nomor_resi,
    p.biaya_pengiriman,
    p.status,
    p.total_item,
    p.total_qty,
    p.total_berat,
    p.estimasi_tiba,
    p.tanggal_dikirim,
    p.tanggal_diterima,
    p.penerima,
    p.keterangan,
    p.created_at,
    p.updated_at,
    p.created_by,
    p.updated_by,
    pel.kode as kode_pelanggan,
    pel.nama as nama_pelanggan,
    pel.alamat as alamat_pelanggan,
    pel.no_telepon,
    pes.nomor_pesanan,
    pes.tanggal_pesanan,
    pes.total_harga as total_pesanan,
    pes.status as status_pesanan
FROM pengiriman p
LEFT JOIN pelanggan pel ON p.id_pelanggan = pel.id
LEFT JOIN pesanan pes ON p.id_pesanan = pes.id
ORDER BY p.id DESC;

-- Create view for pengiriman detail with complete info
CREATE OR REPLACE VIEW `v_pengiriman_detail` AS
SELECT 
    pd.id,
    pd.id_pengiriman,
    pd.id_barang,
    pd.id_gudang,
    pd.qty_dikirim,
    pd.berat_satuan,
    pd.total_berat,
    pd.keterangan,
    p.nomor_pengiriman,
    p.tanggal_pengiriman,
    p.status as status_pengiriman,
    b.kode_barang,
    b.nama_barang,
    b.merk,
    b.tipe,
    s.nama_satuan,
    g.kode_gudang,
    g.nama_gudang,
    pel.nama as nama_pelanggan,
    pes.nomor_pesanan
FROM pengiriman_detail pd
LEFT JOIN pengiriman p ON pd.id_pengiriman = p.id
LEFT JOIN barang b ON pd.id_barang = b.id
LEFT JOIN satuan s ON b.satuan_id = s.id
LEFT JOIN gudang g ON pd.id_gudang = g.id
LEFT JOIN pelanggan pel ON p.id_pelanggan = pel.id
LEFT JOIN pesanan pes ON p.id_pesanan = pes.id
ORDER BY p.tanggal_pengiriman DESC, b.nama_barang ASC;

-- Create trigger to update pengiriman totals when detail changes
DELIMITER //
CREATE TRIGGER `trg_update_pengiriman_totals_insert` 
AFTER INSERT ON `pengiriman_detail` 
FOR EACH ROW 
BEGIN
    UPDATE pengiriman 
    SET 
        total_item = (
            SELECT COUNT(*) 
            FROM pengiriman_detail 
            WHERE id_pengiriman = NEW.id_pengiriman
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_dikirim), 0) 
            FROM pengiriman_detail 
            WHERE id_pengiriman = NEW.id_pengiriman
        ),
        total_berat = (
            SELECT COALESCE(SUM(total_berat), 0) 
            FROM pengiriman_detail 
            WHERE id_pengiriman = NEW.id_pengiriman
        ),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.id_pengiriman;
END//

CREATE TRIGGER `trg_update_pengiriman_totals_update` 
AFTER UPDATE ON `pengiriman_detail` 
FOR EACH ROW 
BEGIN
    UPDATE pengiriman 
    SET 
        total_item = (
            SELECT COUNT(*) 
            FROM pengiriman_detail 
            WHERE id_pengiriman = NEW.id_pengiriman
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_dikirim), 0) 
            FROM pengiriman_detail 
            WHERE id_pengiriman = NEW.id_pengiriman
        ),
        total_berat = (
            SELECT COALESCE(SUM(total_berat), 0) 
            FROM pengiriman_detail 
            WHERE id_pengiriman = NEW.id_pengiriman
        ),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.id_pengiriman;
END//

CREATE TRIGGER `trg_update_pengiriman_totals_delete` 
AFTER DELETE ON `pengiriman_detail` 
FOR EACH ROW 
BEGIN
    UPDATE pengiriman 
    SET 
        total_item = (
            SELECT COUNT(*) 
            FROM pengiriman_detail 
            WHERE id_pengiriman = OLD.id_pengiriman
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_dikirim), 0) 
            FROM pengiriman_detail 
            WHERE id_pengiriman = OLD.id_pengiriman
        ),
        total_berat = (
            SELECT COALESCE(SUM(total_berat), 0) 
            FROM pengiriman_detail 
            WHERE id_pengiriman = OLD.id_pengiriman
        ),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = OLD.id_pengiriman;
END//
DELIMITER ;

-- Add indexes for better performance
CREATE INDEX `idx_pengiriman_created_at` ON `pengiriman` (`created_at`);
CREATE INDEX `idx_pengiriman_updated_at` ON `pengiriman` (`updated_at`);
CREATE INDEX `idx_pengiriman_ekspedisi` ON `pengiriman` (`ekspedisi`);
CREATE INDEX `idx_pengiriman_nomor_resi` ON `pengiriman` (`nomor_resi`);

CREATE INDEX `idx_pengiriman_detail_qty` ON `pengiriman_detail` (`qty_dikirim`);
CREATE INDEX `idx_pengiriman_detail_berat` ON `pengiriman_detail` (`total_berat`);

-- Update stok_movement enum to include shipping transaction type
ALTER TABLE `stok_movement` 
MODIFY COLUMN `tipe_transaksi` enum('pembelian','penjualan','retur_beli','retur_jual','penyesuaian','opname','transfer_masuk','transfer_keluar','pengiriman') NOT NULL;

-- Create function to get shipping summary by date range
DELIMITER //
CREATE FUNCTION `get_pengiriman_summary_by_date`(
    start_date DATE,
    end_date DATE
) RETURNS JSON
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE result JSON;
    
    SELECT JSON_OBJECT(
        'total_pengiriman', COUNT(*),
        'total_item', COALESCE(SUM(total_item), 0),
        'total_qty', COALESCE(SUM(total_qty), 0),
        'total_berat', COALESCE(SUM(total_berat), 0),
        'total_biaya', COALESCE(SUM(biaya_pengiriman), 0),
        'status_breakdown', JSON_OBJECT(
            'draft', SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END),
            'prepared', SUM(CASE WHEN status = 'prepared' THEN 1 ELSE 0 END),
            'shipped', SUM(CASE WHEN status = 'shipped' THEN 1 ELSE 0 END),
            'in_transit', SUM(CASE WHEN status = 'in_transit' THEN 1 ELSE 0 END),
            'delivered', SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END),
            'returned', SUM(CASE WHEN status = 'returned' THEN 1 ELSE 0 END),
            'cancelled', SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END)
        )
    ) INTO result
    FROM pengiriman
    WHERE tanggal_pengiriman BETWEEN start_date AND end_date;
    
    RETURN result;
END//
DELIMITER ;

-- Grant necessary permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON pengiriman TO 'your_app_user'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON pengiriman_detail TO 'your_app_user'@'localhost';
-- GRANT SELECT ON v_pengiriman_summary TO 'your_app_user'@'localhost';
-- GRANT SELECT ON v_pengiriman_detail TO 'your_app_user'@'localhost';

-- =====================================================
-- END OF PENGIRIMAN MODULE DATABASE SETUP
-- =====================================================
