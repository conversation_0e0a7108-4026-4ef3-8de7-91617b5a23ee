<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fa fa-truck text-blue"></i> Data Barang Masuk</h3>
                        <div class="text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary add" onclick="add()" title="Add Data"><i class="fas fa-plus"></i> Add</button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <table id="tbl_barang_masuk" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr class="bg-info">
                                    <th>Nomor Penerimaan</th>
                                    <th>Tanggal</th>
                                    <th>Supplier</th>
                                    <th>Jenis</th>
                                    <th>Ref. Nomor</th>
                                    <th>Status</th>
                                    <th>Total Item</th>
                                    <th>Total Qty</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modal Form Header -->
<div class="modal fade" id="modal_form" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Barang Masuk</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Form akan dimuat di sini -->
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSave" onclick="save()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detail Barang Masuk -->
<div class="modal fade" id="modal_detail" role="dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Detail Barang Masuk</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-detail-body">
                <!-- Detail akan dimuat di sini -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<script>
    var table;
    var save_method;

    $(document).ready(function() {
        $('.select2').select2();
        
        //datatables
        table = $("#tbl_barang_masuk").DataTable({
            "responsive": true,
            "autoWidth": false,
            "language": {
                "sEmptyTable": "Data Barang Masuk Belum Ada"
            },
            "processing": true, //Feature control the processing indicator.
            "serverSide": true, //Feature control DataTables' server-side processing mode.
            "order": [], //Initial no order.

            // Load data for the table's content from an Ajax source
            "ajax": {
                "url": "<?php echo site_url('BarangMasuk/ajax_list') ?>",
                "type": "POST"
            },

        });

        //set input/textarea/select event when change value, remove class error and remove text help block
        $("input").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
        $("textarea").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
        $("select").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });

    });

    function add() {
        save_method = 'add';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('#modal_form').modal({
            backdrop: 'static',
            keyboard: false
        }); // show bootstrap modal
        //Ajax Load data from ajax
        $.ajax({
            url: "<?php echo site_url('BarangMasuk/form_input') ?>",
            type: "GET",
            dataType: "html",
            success: function(data) {
                $('#modal-body').html(data);

                // Enable nomor field dan show tombol generate saat add
                setTimeout(function() {
                    $('[name="nomor_penerimaan"]').prop('readonly', false);
                    $('#btn-generate-nomor').show();
                    
                    // Set tanggal hari ini
                    var today = new Date().toISOString().split('T')[0];
                    $('[name="tanggal"]').val(today);
                }, 100);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat form.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
        $('.modal-title').text('Add Barang Masuk'); // Set title to Bootstrap modal title
    }

    function edit(id) {
        save_method = 'update';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string

        //Ajax Load data from ajax
        $.ajax({
            url: "<?php echo site_url('BarangMasuk/form_input') ?>",
            type: "GET",
            dataType: "html",
            success: function(data) {
                $('#modal-body').html(data);

                // Disable nomor field dan hide tombol generate saat edit
                setTimeout(function() {
                    $('[name="nomor_penerimaan"]').prop('readonly', true);
                    $('#btn-generate-nomor').hide();
                }, 100);

                //Ajax Load data from ajax
                $.ajax({
                    url: "<?php echo site_url('BarangMasuk/edit/') ?>" + id,
                    type: "GET",
                    dataType: "JSON",
                    success: function(data) {
                        $('[name="id"]').val(data.id);
                        $('[name="nomor_penerimaan"]').val(data.nomor_penerimaan);
                        $('[name="tanggal"]').val(data.tanggal);
                        $('[name="id_supplier"]').val(data.id_supplier).trigger('change');
                        $('[name="jenis"]').val(data.jenis).trigger('change');
                        $('[name="ref_nomor"]').val(data.ref_nomor);
                        $('[name="keterangan"]').val(data.keterangan);

                        $('#modal_form').modal('show'); // show bootstrap modal when complete loaded
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat memuat data.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat form.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
        $('.modal-title').text('Edit Barang Masuk'); // Set title to Bootstrap modal title
    }

    function detail(id) {
        $('#modal_detail').modal({
            backdrop: 'static',
            keyboard: false
        });
        
        $.ajax({
            url: "<?php echo site_url('BarangMasuk/detail/') ?>" + id,
            type: "GET",
            dataType: "html",
            success: function(data) {
                $('#modal-detail-body').html(data);
                $('#modal_detail').modal('show');
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat detail.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
        
        // Event listener untuk refresh tabel ketika modal detail ditutup
        $('#modal_detail').off('hidden.bs.modal.refresh').on('hidden.bs.modal.refresh', function() {
            table.ajax.reload(null, false);
        });
    }

    function save() {
        $('#btnSave').text('saving...'); //change button text
        $('#btnSave').attr('disabled', true); //set button disable
        var url;

        if (save_method == 'add') {
            url = "<?php echo site_url('BarangMasuk/insert') ?>";
        } else {
            url = "<?php echo site_url('BarangMasuk/update') ?>";
        }

        // ajax adding data to database
        $.ajax({
            url: url,
            type: "POST",
            data: $('#form').serialize(),
            dataType: "JSON",
            success: function(data) {
                if (data.status) //if success close modal and reload ajax table
                {
                    $('#modal_form').modal('hide');
                    table.ajax.reload(null, false);

                    var message = 'Data barang masuk berhasil disimpan.';

                    Swal.fire({
                        title: 'Berhasil!',
                        text: message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                } else {
                    for (var i = 0; i < data.inputerror.length; i++) {
                        $('[name="' + data.inputerror[i] + '"]').addClass('is-invalid');
                        $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]);
                    }

                    // Show toast notification with detailed errors
                    var errorMessages = data.error_string.join('<br>');
                    Swal.fire({
                        title: 'Gagal Menyimpan!',
                        html: 'Terjadi kesalahan:<br>' + errorMessages,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
                $('#btnSave').text('save'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menyimpan data.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                $('#btnSave').text('save'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable
            }
        });
    }

    function hapus(id) {
        Swal.fire({
            title: 'Apakah Anda yakin?',
            text: "Data barang masuk akan dihapus permanen!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Ya, hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                // ajax delete data to database
                $.ajax({
                    url: "<?php echo site_url('BarangMasuk/delete') ?>",
                    type: "POST",
                    dataType: "JSON",
                    data: {
                        id: id
                    },
                    success: function(data) {
                        if (data.status) {
                            table.ajax.reload(null, false);
                            Swal.fire({
                                title: 'Terhapus!',
                                text: 'Data barang masuk berhasil dihapus.',
                                icon: 'success',
                                confirmButtonText: 'OK'
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: 'Gagal menghapus data.',
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat menghapus data.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }

    function finalize(id) {
        Swal.fire({
            title: 'Finalisasi Barang Masuk?',
            text: "Setelah difinalisasi, data tidak dapat diubah lagi dan akan otomatis update stok sistem!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Ya, finalisasi!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: "<?php echo site_url('BarangMasuk/finalize') ?>",
                    type: "POST",
                    dataType: "JSON",
                    data: {
                        id: id
                    },
                    success: function(data) {
                        if (data.status) {
                            table.ajax.reload(null, false);
                            Swal.fire({
                                title: 'Berhasil!',
                                text: 'Barang masuk berhasil difinalisasi dan stok sistem telah diupdate.',
                                icon: 'success',
                                confirmButtonText: 'OK'
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: data.message || 'Gagal finalisasi barang masuk.',
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat finalisasi.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }

    // Function untuk generate nomor otomatis
    function generateNomor() {
        $.ajax({
            url: "<?php echo site_url('BarangMasuk/generate_nomor') ?>",
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                $('[name="nomor_penerimaan"]').val(data.nomor);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Gagal generate nomor.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function print_barang_masuk(id) {
        var url = "<?php echo site_url('BarangMasuk/cetak_barang_masuk/') ?>" + id;
        window.open(url, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
    }
</script>
