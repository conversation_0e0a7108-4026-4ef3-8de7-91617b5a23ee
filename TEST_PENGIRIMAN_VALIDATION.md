# Test Validasi Pengiriman - Pembatasan Item Berdasarkan Pesanan

## Fitur yang Diimplementasikan

### 1. Dropdown Barang Terbatas
- Dropdown barang di form detail pengiriman hanya menampilkan barang dari pesanan yang belum terpenuhi
- Menampilkan informasi sisa qty yang belum dikirim
- Format: "KODE - NAMA BARANG [Sisa: X SATUAN]"

### 2. Validasi Qty Berdasarkan Pesanan
- Qty yang diinput tidak boleh melebihi sisa qty pesanan
- Validasi dilakukan di backend dan frontend
- Pesan error yang informatif

### 3. Informasi Visual
- Menampilkan sisa qty pesanan di form
- Input field memiliki atribut max sesuai sisa qty
- Validasi real-time saat user mengetik

## File yang Dimodifikasi

### 1. Model: `application/models/Mod_pengiriman.php`
- **Method baru**: `get_barang_available_for_shipping()` - Mendapatkan barang yang tersedia untuk pengiriman
- **Method baru**: `validate_qty_against_order()` - Validasi qty berdasarkan pesanan

### 2. Controller: `application/controllers/Pengiriman.php`
- **Method baru**: `get_barang_available_for_shipping()` - Endpoint untuk AJAX
- **Modifikasi**: `insert_detail()` - Menambah validasi qty pesanan
- **Modifikasi**: `update_detail()` - Menambah validasi qty pesanan
- **Modifikasi**: `detail()` - Menghapus barang_list yang tidak diperlukan

### 3. View: `application/views/pengiriman/detail_pengiriman.php`
- **Modifikasi**: Dropdown barang dimuat dinamis via AJAX
- **Modifikasi**: Menambah informasi qty remaining
- **Modifikasi**: Validasi real-time di frontend
- **Function baru**: `load_available_barang()` - Memuat barang yang tersedia

## Cara Testing

### 1. Persiapan Data
```sql
-- Pastikan ada pesanan dengan status 'diproses'
-- Pastikan ada barang dalam pesanan
-- Pastikan ada stok di gudang
```

### 2. Test Case 1: Tambah Item Baru
1. Buka detail pengiriman
2. Klik "Tambah Item"
3. Verifikasi dropdown barang hanya menampilkan barang dari pesanan
4. Pilih barang, verifikasi informasi sisa qty muncul
5. Input qty melebihi sisa, verifikasi error muncul
6. Input qty valid, verifikasi berhasil disimpan

### 3. Test Case 2: Edit Item Existing
1. Edit item yang sudah ada
2. Verifikasi dropdown tetap terbatas pada barang pesanan
3. Verifikasi qty validation tetap bekerja

### 4. Test Case 3: Multiple Partial Shipments
1. Buat pengiriman pertama dengan qty sebagian
2. Buat pengiriman kedua
3. Verifikasi sisa qty sudah dikurangi dari pengiriman pertama

## Expected Behavior

### ✅ Yang Harus Berhasil:
- Dropdown hanya menampilkan barang dari pesanan yang belum terpenuhi
- Qty input dibatasi sesuai sisa pesanan
- Validasi error message yang jelas
- Multiple pengiriman parsial untuk pesanan yang sama

### ❌ Yang Harus Gagal:
- Menambah barang yang tidak ada di pesanan
- Input qty melebihi sisa pesanan
- Input qty melebihi stok gudang

## Validasi Backend

### 1. Validasi Qty vs Pesanan
```php
$order_validation = $this->Mod_pengiriman->validate_qty_against_order($id_pengiriman, $id_barang, $qty_dikirim);
if (!$order_validation['valid']) {
    // Return error message
}
```

### 2. Validasi Stok Gudang
```php
$stock_validation = $this->Mod_pengiriman->validate_stock($id_barang, $id_gudang, $qty_dikirim);
if (!$stock_validation['is_sufficient']) {
    // Return error message
}
```

## Validasi Frontend

### 1. Real-time Validation
- Input field memiliki atribut `max` sesuai sisa qty
- Event handler `input` untuk validasi real-time
- Visual feedback dengan class `is-invalid`

### 2. Informasi Visual
- Menampilkan sisa qty di bawah input field
- Placeholder input menunjukkan max qty
- Dropdown option menampilkan sisa qty

## Troubleshooting

### Jika dropdown kosong:
1. Cek apakah pesanan memiliki status yang benar
2. Cek apakah ada item pesanan yang belum terpenuhi
3. Cek console browser untuk error AJAX

### Jika validasi tidak bekerja:
1. Cek method `validate_qty_against_order()` di model
2. Cek apakah validasi dipanggil di controller
3. Cek JavaScript validation di frontend

## Notes
- Implementasi ini memastikan integritas data antara pesanan dan pengiriman
- Mencegah over-shipment yang dapat menyebabkan masalah inventory
- Memberikan user experience yang lebih baik dengan informasi yang jelas
