<!-- Form Input Pesanan - Updated with Status Standardization -->
<div class="modal-header">
    <h4 class="modal-title">
        <i class="fas fa-shopping-cart"></i> 
        <?= isset($pesanan) ? 'Edit Pesanan' : '<PERSON><PERSON> Pesanan' ?>
    </h4>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>

<form action="#" id="form" class="form-horizontal">
    <div class="modal-body">
        <input type="hidden" value="<?= isset($pesanan) ? $pesanan->id : '' ?>" name="id"/>
        
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle"></i> Informasi Pesanan
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group row">
                                    <label for="nomor_pesanan" class="col-sm-4 col-form-label">Nomor Pesanan <span class="text-danger">*</span></label>
                                    <div class="col-sm-8 kosong">
                                        <div class="input-group">
                                            <input type="text" class="form-control" name="nomor_pesanan" id="nomor_pesanan" 
                                                   placeholder="Auto Generate" value="<?= isset($pesanan) ? $pesanan->nomor_pesanan : $nomor_pesanan ?>" readonly>
                                            <div class="input-group-append">
                                                <button class="btn btn-outline-secondary" type="button" onclick="generateNomor()">
                                                    <i class="fas fa-sync-alt"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <span class="help-block"></span>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="tanggal_pesanan" class="col-sm-4 col-form-label">Tanggal Pesanan <span class="text-danger">*</span></label>
                                    <div class="col-sm-8 kosong">
                                        <input type="date" class="form-control" name="tanggal_pesanan" id="tanggal_pesanan" 
                                               value="<?= isset($pesanan) ? $pesanan->tanggal_pesanan : date('Y-m-d') ?>" required>
                                        <span class="help-block"></span>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="id_pelanggan" class="col-sm-4 col-form-label">Pelanggan <span class="text-danger">*</span></label>
                                    <div class="col-sm-8 kosong">
                                        <select class="form-control select2" name="id_pelanggan" id="id_pelanggan" required style="width: 100%;">
                                            <option value="">-- Pilih Pelanggan --</option>
                                            <?php foreach($pelanggan_list as $pelanggan): ?>
                                                <option value="<?= $pelanggan->id ?>" 
                                                    <?= (isset($pesanan) && $pesanan->id_pelanggan == $pelanggan->id) ? 'selected' : '' ?>>
                                                    <?= $pelanggan->kode ?> - <?= $pelanggan->nama ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <span class="help-block"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group row">
                                    <label for="jenis_pesanan" class="col-sm-4 col-form-label">Jenis Pesanan <span class="text-danger">*</span></label>
                                    <div class="col-sm-8 kosong">
                                        <select class="form-control select2" name="jenis_pesanan" id="jenis_pesanan" required style="width: 100%;">
                                            <option value="">-- Pilih Jenis --</option>
                                            <option value="manual" <?= (isset($pesanan) && $pesanan->jenis_pesanan == 'manual') ? 'selected' : 'selected' ?>>Manual</option>
                                            <option value="android" <?= (isset($pesanan) && $pesanan->jenis_pesanan == 'android') ? 'selected' : '' ?>>Android</option>
                                        </select>
                                        <span class="help-block"></span>
                                    </div>
                                </div>

                                <!-- Status Dropdown using Status Helper -->
                                <div class="form-group row">
                                    <label for="status" class="col-sm-4 col-form-label">Status <span class="text-danger">*</span></label>
                                    <div class="col-sm-8 kosong">
                                        <select class="form-control select2" name="status" id="status" required style="width: 100%;">
                                            <?php 
                                            $current_status = isset($pesanan) ? $pesanan->status : 'draft';
                                            foreach($status_options as $value => $label): 
                                            ?>
                                                <option value="<?= $value ?>" 
                                                    <?= ($current_status == $value) ? 'selected' : '' ?>
                                                    data-badge-class="<?= get_status_class('pesanan', $value) ?>"
                                                    data-color="<?= get_status_color('pesanan', $value) ?>">
                                                    <?= $label ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <span class="help-block"></span>
                                        
                                        <!-- Status Preview -->
                                        <div class="mt-2">
                                            <small class="text-muted">Preview: </small>
                                            <span id="status-preview">
                                                <?= get_status_badge('pesanan', $current_status) ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="keterangan" class="col-sm-4 col-form-label">Keterangan</label>
                                    <div class="col-sm-8">
                                        <textarea class="form-control" name="keterangan" id="keterangan" rows="3" 
                                                  placeholder="Keterangan tambahan..."><?= isset($pesanan) ? $pesanan->keterangan : '' ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Status Transition Info -->
                        <?php if (isset($pesanan)): ?>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle"></i> Informasi Status</h6>
                                    <p class="mb-1">
                                        <strong>Status Saat Ini:</strong> 
                                        <?= get_status_badge('pesanan', $pesanan->status) ?>
                                    </p>
                                    <?php 
                                    $transitions = get_status_transitions();
                                    $allowed_transitions = $transitions['pesanan'][$pesanan->status] ?? [];
                                    if (!empty($allowed_transitions)): 
                                    ?>
                                    <p class="mb-0">
                                        <strong>Status yang Dapat Dipilih:</strong>
                                        <?php foreach($allowed_transitions as $next_status): ?>
                                            <?= get_status_badge('pesanan', $next_status) ?>
                                        <?php endforeach; ?>
                                    </p>
                                    <?php else: ?>
                                    <p class="mb-0">
                                        <small class="text-muted">Status ini sudah final, tidak dapat diubah lagi.</small>
                                    </p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">
            <i class="fas fa-times"></i> Tutup
        </button>
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-save"></i> Simpan
        </button>
    </div>
</form>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap4',
        width: '100%'
    });

    // Status change preview
    $('#status').on('change', function() {
        var selectedOption = $(this).find('option:selected');
        var badgeClass = selectedOption.data('badge-class');
        var statusText = selectedOption.text();
        
        $('#status-preview').html('<span class="badge ' + badgeClass + '">' + statusText + '</span>');
    });

    // Form validation and submission
    $("#form").submit(function(e) {
        e.preventDefault();
        var form = $(this);
        
        // Reset previous validation states
        $('.form-control').removeClass('is-invalid');
        $('.help-block').empty();
        
        // Show loading
        var submitBtn = form.find('button[type="submit"]');
        var originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...').prop('disabled', true);
        
        $.ajax({
            url: "<?= site_url('pesanan/' . (isset($pesanan) ? 'update' : 'insert')) ?>",
            type: "POST",
            data: form.serialize(),
            dataType: "JSON",
            success: function(data) {
                if(data.status) {
                    $('#modal_form').modal('hide');
                    toastr.success('Data pesanan berhasil disimpan!');
                    table.ajax.reload();
                } else {
                    // Show validation errors
                    for (var i = 0; i < data.inputerror.length; i++) {
                        $('[name="' + data.inputerror[i] + '"]').addClass('is-invalid');
                        $('[name="' + data.inputerror[i] + '"]').next('.help-block').text(data.error_string[i]);
                    }
                    
                    if (data.message) {
                        toastr.error(data.message);
                    }
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                toastr.error('Terjadi kesalahan sistem!');
            },
            complete: function() {
                // Restore button
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });
});

function generateNomor() {
    $.ajax({
        url: "<?= site_url('pesanan/generate_nomor') ?>",
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            $('#nomor_pesanan').val(data.nomor);
        },
        error: function() {
            toastr.error('Gagal generate nomor pesanan!');
        }
    });
}
</script>
