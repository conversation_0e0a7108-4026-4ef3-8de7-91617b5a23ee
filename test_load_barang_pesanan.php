<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Load Barang dari <PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h2>Test Load Barang dari Pesanan</h2>
                <p>Halaman ini untuk menguji fungsi load barang dari pesanan ke pengiriman.</p>
                
                <div class="card">
                    <div class="card-header">
                        <h5>Test Functionality</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="id_pengiriman" class="form-label">ID Pengiriman:</label>
                            <input type="number" class="form-control" id="id_pengiriman" value="2" placeholder="Masukkan ID pengiriman">
                            <small class="text-muted">Gunakan ID pengiriman yang sudah ada (contoh: 2)</small>
                        </div>
                        
                        <button type="button" class="btn btn-primary" onclick="testLoadFromPesanan()">
                            Test Load dari Pesanan
                        </button>
                        
                        <button type="button" class="btn btn-info" onclick="checkPengirimanData()">
                            Check Pengiriman Data
                        </button>
                        
                        <button type="button" class="btn btn-success" onclick="checkPesananData()">
                            Check Pesanan Data
                        </button>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="results">
                            <p class="text-muted">Hasil test akan ditampilkan di sini...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testLoadFromPesanan() {
            const id_pengiriman = $('#id_pengiriman').val();
            
            if (!id_pengiriman) {
                Swal.fire('Error', 'Masukkan ID pengiriman terlebih dahulu', 'error');
                return;
            }
            
            Swal.fire({
                title: 'Loading...',
                text: 'Sedang menguji fungsi load dari pesanan',
                allowOutsideClick: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            $.ajax({
                url: 'application/controllers/Pengiriman.php',
                type: 'POST',
                data: {
                    action: 'load_items_from_pesanan',
                    id_pengiriman: id_pengiriman
                },
                dataType: 'json',
                success: function(response) {
                    Swal.close();
                    displayResults('Load dari Pesanan', response);
                },
                error: function(xhr, status, error) {
                    Swal.close();
                    displayResults('Load dari Pesanan - Error', {
                        status: false,
                        message: 'Error: ' + error,
                        xhr_status: status,
                        response_text: xhr.responseText
                    });
                }
            });
        }
        
        function checkPengirimanData() {
            const id_pengiriman = $('#id_pengiriman').val();
            
            if (!id_pengiriman) {
                Swal.fire('Error', 'Masukkan ID pengiriman terlebih dahulu', 'error');
                return;
            }
            
            // Simulate checking pengiriman data
            displayResults('Check Pengiriman Data', {
                status: true,
                message: 'Checking pengiriman ID: ' + id_pengiriman,
                note: 'Ini adalah simulasi. Dalam implementasi nyata, akan mengambil data dari database.'
            });
        }
        
        function checkPesananData() {
            // Simulate checking pesanan data
            displayResults('Check Pesanan Data', {
                status: true,
                message: 'Checking available pesanan data',
                note: 'Ini adalah simulasi. Dalam implementasi nyata, akan mengambil data pesanan yang bisa dikirim.'
            });
        }
        
        function displayResults(title, data) {
            const timestamp = new Date().toLocaleString();
            const resultHtml = `
                <div class="alert ${data.status ? 'alert-success' : 'alert-danger'} mb-3">
                    <h6><strong>${title}</strong> - ${timestamp}</h6>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
            
            $('#results').prepend(resultHtml);
        }
    </script>
</body>
</html>
