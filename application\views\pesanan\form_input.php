<form action="#" id="form" class="form-horizontal">
    <input type="hidden" value="" name="id" />
    <div class="card-body">

        <!-- Tab Navigation -->
        <ul class="nav nav-tabs" id="pesananTab" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" id="basic-tab" data-toggle="tab" href="#basic" role="tab">Data Dasar</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="pelanggan-tab" data-toggle="tab" href="#pelanggan" role="tab">Pelanggan</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="detail-tab" data-toggle="tab" href="#detail" role="tab">Detail & Keterangan</a>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="pesananTabContent">

            <!-- Tab Data Dasar -->
            <div class="tab-pane fade show active" id="basic" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="nomor_pesanan" class="col-sm-3 col-form-label">Nomor Pesanan</label>
                        <div class="col-sm-7 kosong">
                            <input type="text" class="form-control" name="nomor_pesanan" id="nomor_pesanan" placeholder="Nomor akan di-generate otomatis" value="<?= isset($nomor_pesanan) ? $nomor_pesanan : '' ?>" autocomplete="off">
                            <small class="form-text text-muted">Format: PSN-2025-001, PSN-2025-002, dst. Kosongkan untuk generate otomatis.</small>
                            <span class="help-block"></span>
                        </div>
                        <div class="col-sm-2">
                            <button type="button" id="btn-generate-nomor" class="btn btn-outline-secondary" onclick="generateNomor()" title="Generate Nomor Otomatis">
                                <i class="fas fa-sync"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="tanggal_pesanan" class="col-sm-3 col-form-label">Tanggal Pesanan <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <input type="date" class="form-control" name="tanggal_pesanan" id="tanggal_pesanan" value="<?= isset($pesanan->tanggal_pesanan) ? $pesanan->tanggal_pesanan : date('Y-m-d') ?>" required>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="jenis_pesanan" class="col-sm-3 col-form-label">Jenis Pesanan <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <select class="form-control select2" name="jenis_pesanan" id="jenis_pesanan" required style="width: 100%;" readonly>
                                <option value="">-- Pilih Jenis --</option>
                                <option value="manual" <?= (isset($pesanan) && $pesanan->jenis_pesanan == 'manual') ? 'selected' : '' ?> 'selected'>Manual</option>
                                <option value="android" <?= (isset($pesanan) && $pesanan->jenis_pesanan == 'android') ? 'selected' : '' ?>>Android</option>
                            </select>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="status" class="col-sm-3 col-form-label">Status <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <select class="form-control select2" name="status" id="status" required style="width: 100%;">
                                <option value="draft" <?= (isset($pesanan) && $pesanan->status == 'draft') ? 'selected' : 'selected' ?>>Draft</option>
                                <option value="diproses" <?= (isset($pesanan) && $pesanan->status == 'diproses') ? 'selected' : '' ?>>Diproses</option>
                                <option value="dikirim" <?= (isset($pesanan) && $pesanan->status == 'dikirim') ? 'selected' : '' ?>>Dikirim</option>
                                <option value="selesai" <?= (isset($pesanan) && $pesanan->status == 'selesai') ? 'selected' : '' ?>>Selesai</option>
                                <option value="dibatalkan" <?= (isset($pesanan) && $pesanan->status == 'dibatalkan') ? 'selected' : '' ?>>Dibatalkan</option>
                            </select>
                            <span class="help-block"></span>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Tab Pelanggan -->
            <div class="tab-pane fade" id="pelanggan" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="id_pelanggan" class="col-sm-3 col-form-label">Pelanggan <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <select class="form-control select2" name="id_pelanggan" id="id_pelanggan" required style="width: 100%;">
                                <option value="">-- Pilih Pelanggan --</option>
                                <?php if(isset($pelanggan_list)): ?>
                                    <?php foreach($pelanggan_list as $pelanggan): ?>
                                        <option value="<?= $pelanggan->id ?>" <?= (isset($pesanan) && $pesanan->id_pelanggan == $pelanggan->id) ? 'selected' : '' ?>>
                                            <?= $pelanggan->kode ?> - <?= $pelanggan->nama ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                            <small class="form-text text-muted">Pilih pelanggan yang akan melakukan pesanan.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div id="pelanggan-info" style="display: none;">
                        <div class="alert alert-info">
                            <h6><i class="fa fa-info-circle"></i> Informasi Pelanggan</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <small><strong>Alamat:</strong> <span id="pelanggan-alamat">-</span></small><br>
                                    <small><strong>No. Telepon:</strong> <span id="pelanggan-telepon">-</span></small>
                                </div>
                                <div class="col-md-6">
                                    <small><strong>Email:</strong> <span id="pelanggan-email">-</span></small><br>
                                    <small><strong>PIC:</strong> <span id="pelanggan-pic">-</span></small>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Tab Detail & Keterangan -->
            <div class="tab-pane fade" id="detail" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="keterangan" class="col-sm-3 col-form-label">Keterangan</label>
                        <div class="col-sm-9 kosong">
                            <textarea class="form-control" name="keterangan" id="keterangan" placeholder="Keterangan tambahan untuk pesanan ini" rows="4"><?= isset($pesanan->keterangan) ? $pesanan->keterangan : '' ?></textarea>
                            <small class="form-text text-muted">Informasi tambahan mengenai pesanan ini.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <hr>
                    <h6 class="text-muted">Informasi Tambahan</h6>

                    <div class="alert alert-warning">
                        <i class="fa fa-info-circle"></i> <strong>Catatan:</strong>
                        <ul class="mb-0 mt-2">
                            <li>Pesanan dengan status <strong>Draft</strong> masih dapat diedit dan dihapus</li>
                            <li>Setelah diproses, pesanan tidak dapat diedit lagi</li>
                            <li>Item pesanan dapat ditambahkan setelah pesanan disimpan</li>
                        </ul>
                    </div>

                </div>
            </div>

        </div>

    </div>
</form>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        dropdownParent: $('#modal_form'),
        placeholder: "-- Pilih --",
        allowClear: true
    });

    // Event handler untuk perubahan pelanggan
    $('#id_pelanggan').on('change', function() {
        var pelangganId = $(this).val();

        if (pelangganId) {
            // Ambil data pelanggan via AJAX
            $.ajax({
                url: "<?php echo site_url('pelanggan/get_pelanggan_info') ?>",
                type: "POST",
                data: { id: pelangganId },
                dataType: "JSON",
                success: function(data) {
                    if (data.status) {
                        $('#pelanggan-alamat').text(data.data.alamat || '-');
                        $('#pelanggan-telepon').text(data.data.no_telepon || '-');
                        $('#pelanggan-email').text(data.data.email || '-');
                        $('#pelanggan-pic').text(data.data.nama_pic || '-');
                        $('#pelanggan-info').show();
                    }
                },
                error: function() {
                    $('#pelanggan-info').hide();
                }
            });
        } else {
            $('#pelanggan-info').hide();
        }
    });

    // Trigger change event untuk set initial state
    $('#id_pelanggan').trigger('change');
});

function generateNomor() {
    $.ajax({
        url: "<?php echo site_url('pesanan/generate_nomor') ?>",
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            if (data.nomor) {
                $('#nomor_pesanan').val(data.nomor);
            }
        },
        error: function() {
            Swal.fire({
                title: 'Error!',
                text: 'Gagal generate nomor pesanan.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    });
}
</script>