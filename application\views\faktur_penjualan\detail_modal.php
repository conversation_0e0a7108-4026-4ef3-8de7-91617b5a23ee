<div class="modal-header">
    <h4 class="modal-title">
        <i class="fas fa-file-invoice"></i> Detail Faktur - <?= $faktur->nomor_faktur ?>
    </h4>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>

<div class="modal-body">
    <!-- Header Info -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary">
                    <h5 class="card-title mb-0"><i class="fas fa-file-invoice"></i> Informasi Faktur</h5>
                </div>
                <div class="card-body">
                    <table class="table table-sm table-borderless">
                        <tr>
                            <td width="40%"><strong>Nomor Faktur</strong></td>
                            <td>: <?= $faktur->nomor_faktur ?></td>
                        </tr>
                        <tr>
                            <td><strong>Tanggal Faktur</strong></td>
                            <td>: <?= date('d/m/Y', strtotime($faktur->tanggal_faktur)) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Jatuh Tempo</strong></td>
                            <td>: <?= $faktur->tanggal_jatuh_tempo ? date('d/m/Y', strtotime($faktur->tanggal_jatuh_tempo)) : '-' ?></td>
                        </tr>
                        <tr>
                            <td><strong>Jenis Faktur</strong></td>
                            <td>:
                                <span class="badge <?= $faktur->jenis_faktur == 'pajak' ? 'badge-info' : 'badge-secondary' ?>">
                                    <?= ucfirst($faktur->jenis_faktur) ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Status</strong></td>
                            <td>:
                                <?php
                                $status_class = '';
                                switch ($faktur->status) {
                                    case 'draft':
                                        $status_class = 'badge-warning';
                                        break;
                                    case 'diterbitkan':
                                        $status_class = 'badge-info';
                                        break;
                                    case 'dibayar':
                                        $status_class = 'badge-success';
                                        break;
                                    case 'overdue':
                                        $status_class = 'badge-danger';
                                        break;
                                    case 'dibatalkan':
                                        $status_class = 'badge-secondary';
                                        break;
                                }
                                ?>
                                <span class="badge <?= $status_class ?>"><?= ucfirst($faktur->status) ?></span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>No. Pesanan</strong></td>
                            <td>: <?= $faktur->nomor_pesanan ?: '-' ?></td>
                        </tr>
                        <tr>
                            <td><strong>No. Pengiriman</strong></td>
                            <td>: <?= $faktur->nomor_pengiriman ?: '-' ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info">
                    <h5 class="card-title mb-0"><i class="fas fa-user"></i> Informasi Pelanggan</h5>
                </div>
                <div class="card-body">
                    <table class="table table-sm table-borderless">
                        <tr>
                            <td width="40%"><strong>Kode Pelanggan</strong></td>
                            <td>: <?= $faktur->kode_pelanggan ?></td>
                        </tr>
                        <tr>
                            <td><strong>Nama Pelanggan</strong></td>
                            <td>: <?= $faktur->nama_pelanggan ?></td>
                        </tr>
                        <tr>
                            <td><strong>Telepon</strong></td>
                            <td>: <?= $faktur->no_telepon ?: '-' ?></td>
                        </tr>
                        <tr>
                            <td><strong>NPWP</strong></td>
                            <td>: <?= $faktur->npwp ?: '-' ?></td>
                        </tr>
                        <tr>
                            <td><strong>PKP</strong></td>
                            <td>: <?= $faktur->is_pkp ? 'Ya' : 'Tidak' ?></td>
                        </tr>
                        <tr>
                            <td><strong>Alamat Penagihan</strong></td>
                            <td>: <?= $faktur->alamat_penagihan ?: '-' ?></td>
                        </tr>
                        <tr>
                            <td><strong>Sales Person</strong></td>
                            <td>: <?= $faktur->sales_person ?: '-' ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary">
                    <h5 class="card-title mb-0"><i class="fas fa-cogs"></i> Aksi</h5>
                </div>
                <div class="card-body">
                    <?php if ($faktur->status == 'draft'): ?>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="addDetailItem()">
                        <i class="fas fa-plus"></i> Tambah Item
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-warning" onclick="editFaktur(<?= $faktur->id ?>)">
                        <i class="fas fa-edit"></i> Edit Faktur
                    </button>
                    <?php if ($faktur->id_pesanan): ?>
                    <button type="button" class="btn btn-sm btn-outline-info" onclick="copyFromPesanan()">
                        <i class="fas fa-copy"></i> Copy dari Pesanan
                    </button>
                    <?php endif; ?>
                    <?php if ($faktur->id_pengiriman): ?>
                    <button type="button" class="btn btn-sm btn-outline-info" onclick="copyFromPengiriman()">
                        <i class="fas fa-copy"></i> Copy dari Pengiriman
                    </button>
                    <?php endif; ?>
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="updateStatus('diterbitkan')">
                        <i class="fas fa-check"></i> Terbitkan Faktur
                    </button>
                    <?php elseif ($faktur->status == 'diterbitkan'): ?>
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="updateStatus('dibayar')">
                        <i class="fas fa-money-bill"></i> Tandai Dibayar
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="updateStatus('overdue')">
                        <i class="fas fa-exclamation-triangle"></i> Tandai Overdue
                    </button>
                    <?php elseif ($faktur->status == 'dibayar'): ?>
                    <span class="text-success"><i class="fas fa-check-circle"></i> Faktur telah dibayar</span>
                    <?php endif; ?>

                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="printFakturDetail()">
                        <i class="fas fa-print"></i> Print Faktur
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Info -->
    <div class="row mt-3">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-warning">
                    <h5 class="card-title mb-0"><i class="fas fa-credit-card"></i> Informasi Pembayaran</h5>
                </div>
                <div class="card-body">
                    <table class="table table-sm table-borderless">
                        <tr>
                            <td width="40%"><strong>Syarat Pembayaran</strong></td>
                            <td>: <?= $faktur->syarat_pembayaran ?: '-' ?></td>
                        </tr>
                        <tr>
                            <td><strong>Metode Pembayaran</strong></td>
                            <td>: <?= ucfirst($faktur->metode_pembayaran) ?></td>
                        </tr>
                        <tr>
                            <td><strong>No. PO Pelanggan</strong></td>
                            <td>: <?= $faktur->nomor_po_pelanggan ?: '-' ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success">
                    <h5 class="card-title mb-0"><i class="fas fa-calculator"></i> Ringkasan Total</h5>
                </div>
                <div class="card-body">
                    <table class="table table-sm table-borderless">
                        <tr>
                            <td width="40%"><strong>Total Item</strong></td>
                            <td>: <?= number_format($faktur->total_item, 0) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Total Qty</strong></td>
                            <td>: <?= number_format($faktur->total_qty, 0) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Subtotal</strong></td>
                            <td>: Rp <?= number_format($faktur->subtotal, 0, ',', '.') ?></td>
                        </tr>
                        <tr>
                            <td><strong>Diskon</strong></td>
                            <td>:
                                <?php if ($faktur->diskon_persen > 0): ?>
                                    <?= $faktur->diskon_persen ?>%
                                <?php endif; ?>
                                <?php if ($faktur->diskon_nominal > 0): ?>
                                    Rp <?= number_format($faktur->diskon_nominal, 0, ',', '.') ?>
                                <?php endif; ?>
                                <?php if ($faktur->diskon_persen == 0 && $faktur->diskon_nominal == 0): ?>
                                    -
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>PPN (<?= $faktur->ppn_persen ?>%)</strong></td>
                            <td>: Rp <?= number_format($faktur->ppn_nominal, 0, ',', '.') ?></td>
                        </tr>
                        <tr class="border-top">
                            <td><strong>Total Akhir</strong></td>
                            <td>: <strong>Rp <?= number_format($faktur->total_setelah_pajak, 0, ',', '.') ?></strong></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Detail Items -->
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary">
                    <h5 class="card-title mb-0"><i class="fas fa-boxes"></i> Detail Item Faktur</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-sm">
                            <thead class="bg-light">
                                <tr>
                                    <th>No</th>
                                    <th>Kode Barang</th>
                                    <th>Nama Barang</th>
                                    <th>Merk/Tipe</th>
                                    <th>Gudang</th>
                                    <th>Qty</th>
                                    <th>Satuan</th>
                                    <th>Harga Satuan</th>
                                    <th>Diskon</th>
                                    <th>Total</th>
                                    <?php if ($faktur->status == 'draft'): ?>
                                    <th>Aksi</th>
                                    <?php endif; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($faktur_detail)): ?>
                                    <?php $no = 1; foreach ($faktur_detail as $d): ?>
                                        <tr>
                                            <td><?= $no++ ?></td>
                                            <td><?= $d->kode_barang ?></td>
                                            <td><?= $d->nama_barang ?></td>
                                            <td><?= $d->merk ? $d->merk . ' ' . $d->tipe : '-' ?></td>
                                            <td><?= $d->nama_gudang ?></td>
                                            <td class="text-right"><?= number_format($d->qty, 0) ?></td>
                                            <td><?= $d->nama_satuan ?></td>
                                            <td class="text-right">Rp <?= number_format($d->harga_satuan, 0, ',', '.') ?></td>
                                            <td class="text-right">
                                                <?php if ($d->diskon_persen > 0): ?>
                                                    <?= $d->diskon_persen ?>%
                                                <?php endif; ?>
                                                <?php if ($d->diskon_nominal > 0): ?>
                                                    Rp <?= number_format($d->diskon_nominal, 0, ',', '.') ?>
                                                <?php endif; ?>
                                                <?php if ($d->diskon_persen == 0 && $d->diskon_nominal == 0): ?>
                                                    -
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-right">Rp <?= number_format($d->total_akhir, 0, ',', '.') ?></td>
                                            <?php if ($faktur->status == 'draft'): ?>
                                            <td>
                                                <button type="button" class="btn btn-xs btn-outline-primary" onclick="editDetailItem(<?= $d->id ?>)" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-xs btn-outline-danger" onclick="deleteDetailItem(<?= $d->id ?>)" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                            <?php endif; ?>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="<?= $faktur->status == 'draft' ? '11' : '10' ?>" class="text-center text-muted">
                                            <i class="fas fa-inbox"></i> Belum ada item dalam faktur ini
                                            <?php if ($faktur->status == 'draft'): ?>
                                                <br><button type="button" class="btn btn-sm btn-outline-primary mt-2" onclick="addDetailItem()">
                                                    <i class="fas fa-plus"></i> Tambah Item Pertama
                                                </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if ($faktur->keterangan): ?>
    <!-- Keterangan -->
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary">
                    <h5 class="card-title mb-0"><i class="fas fa-comment"></i> Keterangan</h5>
                </div>
                <div class="card-body">
                    <p><?= nl2br(htmlspecialchars($faktur->keterangan)) ?></p>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<div class="modal-footer">
    <button type="button" class="btn btn-info" onclick="printFakturDetail()">
        <i class="fas fa-print"></i> Print Faktur
    </button>
    <button type="button" class="btn btn-warning" onclick="editFaktur(<?= $faktur->id ?>); $('#modal_detail').modal('hide');">
        <i class="fas fa-edit"></i> Edit
    </button>
    <button type="button" class="btn btn-secondary" data-dismiss="modal">
        <i class="fas fa-times"></i> Tutup
    </button>
</div>

<script>
var save_method_detail_item;
var current_faktur_id = <?= $faktur->id ?>;

$(document).ready(function() {
    // Initialize Select2 untuk modal detail item
    $('#modal_form_detail_item').on('shown.bs.modal', function() {
        $('.select2').select2({
            dropdownParent: $('#modal_form_detail_item'),
            placeholder: "-- Pilih --",
            allowClear: true
        });
    });

    // Event handler untuk perubahan barang
    $('#id_barang').on('change', function() {
        var selectedOption = $(this).find('option:selected');
        var hargaJual = selectedOption.data('harga');

        if (hargaJual) {
            $('#harga_satuan').val(hargaJual);
            updateTotalHarga();
        }
    });
});

function addDetailItem() {
    save_method_detail_item = 'add';
    $('.form-group').removeClass('has-error');
    $('.help-block').empty();

    // Load form
    $.ajax({
        url: "<?= site_url('FakturPenjualan/add_detail_item') ?>",
        type: "GET",
        dataType: "HTML",
        success: function(data) {
            $('#modal-body-detail-item').html(data);
            $('#modal_form_detail_item').modal('show');
            $('.modal-title').text('Tambah Item Faktur');

            // Reset form after it's loaded
            setTimeout(function() {
                if ($('#form_detail_item').length > 0) {
                    $('#form_detail_item')[0].reset();
                }
            }, 100);
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.error('Error loading detail item form:', textStatus, errorThrown);
            Swal.fire({
                title: 'Error!',
                text: 'Gagal memuat form item: ' + textStatus,
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    });
}

function editDetailItem(id) {
    save_method_detail_item = 'update';
    $('.form-group').removeClass('has-error');
    $('.help-block').empty();

    // Load form
    $.ajax({
        url: "<?= site_url('FakturPenjualan/edit_detail_item/') ?>" + id,
        type: "GET",
        dataType: "HTML",
        success: function(data) {
            $('#modal-body-detail-item').html(data);
            $('#modal_form_detail_item').modal('show');
            $('.modal-title').text('Edit Item Faktur');

            // Don't reset form for edit, it should be populated with existing data
            setTimeout(function() {
                if ($('#form_detail_item').length > 0) {
                    console.log('Edit detail item form loaded successfully');
                }
            }, 100);
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.error('Error loading edit detail item form:', textStatus, errorThrown);
            Swal.fire({
                title: 'Error!',
                text: 'Gagal memuat form edit item: ' + textStatus,
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    });
}

function saveDetailItem() {
    // Check if form exists
    if ($('#form_detail_item').length === 0) {
        Swal.fire({
            title: 'Error!',
            text: 'Form detail item tidak ditemukan!',
            icon: 'error',
            confirmButtonText: 'OK'
        });
        return;
    }

    $('#btnSaveDetailItem').text('Saving...');
    $('#btnSaveDetailItem').attr('disabled', true);

    // Add current faktur ID to form data
    var formData = $('#form_detail_item').serialize() + '&id_faktur=' + current_faktur_id;
    console.log('Saving detail item with data:', formData);

    $.ajax({
        url: "<?= site_url('FakturPenjualan/save_detail_item') ?>",
        type: "POST",
        data: formData,
        dataType: "JSON",
        success: function(data) {
            console.log('Save detail item response:', data);
            if (data.status == 'success') {
                $('#modal_form_detail_item').modal('hide');
                Swal.fire({
                    title: 'Berhasil!',
                    text: data.message,
                    icon: 'success',
                    confirmButtonText: 'OK'
                }).then(() => {
                    // Reload modal detail
                    detail(current_faktur_id);
                    // Reload main table
                    if (typeof table !== 'undefined') {
                        table.ajax.reload();
                    }
                });
            } else {
                Swal.fire({
                    title: 'Error!',
                    text: data.message,
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
            $('#btnSaveDetailItem').text('Save');
            $('#btnSaveDetailItem').attr('disabled', false);
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.error('Save detail item error:', textStatus, errorThrown);
            console.error('Response:', jqXHR.responseText);
            Swal.fire({
                title: 'Error!',
                text: 'Terjadi kesalahan sistem: ' + textStatus,
                icon: 'error',
                confirmButtonText: 'OK'
            });
            $('#btnSaveDetailItem').text('Save');
            $('#btnSaveDetailItem').attr('disabled', false);
        }
    });
}

function deleteDetailItem(id) {
    Swal.fire({
        title: 'Apakah Anda yakin?',
        text: "Item ini akan dihapus dari faktur!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya, hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: "<?= site_url('FakturPenjualan/delete_detail_item') ?>",
                type: "POST",
                dataType: 'json',
                data: {
                    id: id,
                    id_faktur: current_faktur_id
                },
                success: function(response) {
                    if (response.status == 'success') {
                        Swal.fire({
                            title: 'Berhasil!',
                            text: response.message,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then(() => {
                            // Reload modal detail
                            detail(current_faktur_id);
                            // Reload main table
                            if (typeof table !== 'undefined') {
                                table.ajax.reload();
                            }
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: response.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan sistem!',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}

function copyFromPesanan() {
    Swal.fire({
        title: 'Copy Item dari Pesanan?',
        text: "Item yang sudah ada akan diganti dengan item dari pesanan!",
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya, copy!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: "<?= site_url('FakturPenjualan/copy_from_pesanan') ?>",
                type: "POST",
                dataType: 'json',
                data: {
                    id_faktur: current_faktur_id,
                    id_pesanan: <?= $faktur->id_pesanan ?: 'null' ?>
                },
                success: function(response) {
                    if (response.status == 'success') {
                        Swal.fire({
                            title: 'Berhasil!',
                            text: response.message,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then(() => {
                            // Reload modal detail
                            detail(current_faktur_id);
                            // Reload main table
                            if (typeof table !== 'undefined') {
                                table.ajax.reload();
                            }
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: response.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan sistem!',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}

function copyFromPengiriman() {
    Swal.fire({
        title: 'Copy Item dari Pengiriman?',
        text: "Item yang sudah ada akan diganti dengan item dari pengiriman!",
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya, copy!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: "<?= site_url('FakturPenjualan/copy_from_pengiriman') ?>",
                type: "POST",
                dataType: 'json',
                data: {
                    id_faktur: current_faktur_id,
                    id_pengiriman: <?= $faktur->id_pengiriman ?: 'null' ?>
                },
                success: function(response) {
                    if (response.status == 'success') {
                        Swal.fire({
                            title: 'Berhasil!',
                            text: response.message,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then(() => {
                            // Reload modal detail
                            detail(current_faktur_id);
                            // Reload main table
                            if (typeof table !== 'undefined') {
                                table.ajax.reload();
                            }
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: response.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan sistem!',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}

function updateStatus(status) {
    var statusText = '';
    switch(status) {
        case 'diterbitkan':
            statusText = 'menerbitkan';
            break;
        case 'dibayar':
            statusText = 'menandai sebagai dibayar';
            break;
        case 'overdue':
            statusText = 'menandai sebagai overdue';
            break;
        case 'dibatalkan':
            statusText = 'membatalkan';
            break;
    }

    Swal.fire({
        title: 'Konfirmasi',
        text: "Apakah Anda yakin ingin " + statusText + " faktur ini?",
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: "<?= site_url('FakturPenjualan/update_status') ?>",
                type: "POST",
                dataType: 'json',
                data: {
                    id: current_faktur_id,
                    status: status
                },
                success: function(response) {
                    if (response.status == 'success') {
                        Swal.fire({
                            title: 'Berhasil!',
                            text: response.message,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then(() => {
                            // Reload modal detail
                            detail(current_faktur_id);
                            // Reload main table
                            if (typeof table !== 'undefined') {
                                table.ajax.reload();
                            }
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: response.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan sistem!',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}

function editFaktur(id) {
    // Close detail modal first
    $('#modal_detail').modal('hide');

    // Wait for modal to close then open edit modal
    setTimeout(function() {
        edit(id);
    }, 500);
}

function printFaktur() {
    window.open('<?= site_url('FakturPenjualan/print_faktur/' . $faktur->id) ?>', '_blank');
}

function updateTotalHarga() {
    var qty = parseFloat($('#qty').val()) || 0;
    var harga = parseFloat($('#harga_satuan').val()) || 0;
    var diskonPersen = parseFloat($('#diskon_persen').val()) || 0;
    var diskonNominal = parseFloat($('#diskon_nominal').val()) || 0;
    var ppnPersen = parseFloat($('#ppn_persen').val()) || 0;

    var subtotalSebelumDiskon = qty * harga;
    var totalDiskon = (diskonPersen / 100 * subtotalSebelumDiskon) + diskonNominal;
    var subtotalSetelahDiskon = subtotalSebelumDiskon - totalDiskon;
    var ppnNominal = ppnPersen / 100 * subtotalSetelahDiskon;
    var totalAkhir = subtotalSetelahDiskon + ppnNominal;

    $('#subtotal_display').text(totalAkhir.toLocaleString('id-ID'));
}

// Function untuk edit faktur dari detail modal
function editFaktur(id) {
    // Tutup modal detail
    $('#modal_detail').modal('hide');

    // Panggil function edit dari parent window
    if (typeof window.parent.edit === 'function') {
        window.parent.edit(id);
    } else if (typeof edit === 'function') {
        edit(id);
    } else {
        // Fallback - reload form edit
        $.ajax({
            url: "<?= site_url('FakturPenjualan/edit_form/') ?>" + id,
            type: "GET",
            dataType: "HTML",
            success: function(data) {
                $('#modal-body').html(data);
                $('#modal_form').modal('show');
                $('.modal-title').text('Edit Faktur Penjualan');
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error loading form');
            }
        });
    }
}

// Function untuk print faktur dari detail modal
function printFakturDetail() {
    window.open('<?= site_url('FakturPenjualan/print_faktur/') ?>' + current_faktur_id, '_blank');
}

// Function untuk update status faktur
function updateStatus(status) {
    Swal.fire({
        title: 'Konfirmasi',
        text: 'Apakah Anda yakin ingin mengubah status faktur menjadi ' + status + '?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya, ubah!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: "<?= site_url('FakturPenjualan/update_status') ?>",
                type: "POST",
                dataType: 'json',
                data: {
                    id: current_faktur_id,
                    status: status
                },
                success: function(response) {
                    if (response.status == 'success') {
                        Swal.fire({
                            title: 'Berhasil!',
                            text: response.message,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then(() => {
                            // Reload modal detail
                            detail(current_faktur_id);
                            // Reload main table
                            if (typeof table !== 'undefined') {
                                table.ajax.reload();
                            }
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: response.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan sistem!',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}
</script>
