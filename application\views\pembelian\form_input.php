<form action="#" id="form" class="form-horizontal">
    <input type="hidden" name="id" value="">
    
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label for="nomor_pembelian" class="control-label">Nomor Pembelian <span class="text-red">*</span></label>
                <input type="text" class="form-control" id="nomor_pembelian" name="nomor_pembelian" 
                       placeholder="Nomor akan digenerate otomatis" value="<?= $nomor_pembelian ?>" readonly>
                <span class="help-block"></span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="tanggal_pembelian" class="control-label">Tanggal Pembelian <span class="text-red">*</span></label>
                <input type="date" class="form-control" id="tanggal_pembelian" name="tanggal_pembelian" 
                       value="<?= date('Y-m-d') ?>" required>
                <span class="help-block"></span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label for="id_supplier" class="control-label">Supplier <span class="text-red">*</span></label>
                <select class="form-control select2" id="id_supplier" name="id_supplier" style="width: 100%;" required>
                    <option value="">-- Pilih Supplier --</option>
                    <?php foreach ($supplier_list as $supplier): ?>
                        <option value="<?= $supplier->id ?>"><?= $supplier->kode ?> - <?= $supplier->nama ?></option>
                    <?php endforeach; ?>
                </select>
                <span class="help-block"></span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="jenis_pembelian" class="control-label">Jenis Pembelian <span class="text-red">*</span></label>
                <select class="form-control select2" id="jenis_pembelian" name="jenis_pembelian" style="width: 100%;" required>
                    <option value="">-- Pilih Jenis --</option>
                    <option value="reguler">Reguler</option>
                    <option value="konsinyasi">Konsinyasi</option>
                    <option value="kontrak">Kontrak</option>
                </select>
                <span class="help-block"></span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label for="status" class="control-label">Status</label>
                <select class="form-control select2" id="status" name="status" style="width: 100%;">
                    <option value="draft">Draft</option>
                    <option value="disetujui">Disetujui</option>
                    <option value="dipesan">Dipesan</option>
                    <option value="diterima">Diterima</option>
                    <option value="selesai">Selesai</option>
                    <option value="dibatalkan">Dibatalkan</option>
                </select>
                <span class="help-block"></span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="tanggal_jatuh_tempo" class="control-label">Tanggal Jatuh Tempo</label>
                <input type="date" class="form-control" id="tanggal_jatuh_tempo" name="tanggal_jatuh_tempo">
                <span class="help-block"></span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label for="syarat_pembayaran" class="control-label">Syarat Pembayaran</label>
                <input type="text" class="form-control" id="syarat_pembayaran" name="syarat_pembayaran" 
                       placeholder="Contoh: NET 30, COD, dll">
                <span class="help-block"></span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="metode_pembayaran" class="control-label">Metode Pembayaran</label>
                <select class="form-control select2" id="metode_pembayaran" name="metode_pembayaran" style="width: 100%;">
                    <option value="tunai">Tunai</option>
                    <option value="transfer" selected>Transfer</option>
                    <option value="kredit">Kredit</option>
                    <option value="cek">Cek</option>
                    <option value="giro">Giro</option>
                </select>
                <span class="help-block"></span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="form-group">
                <label for="nomor_po_supplier" class="control-label">Nomor PO Supplier</label>
                <input type="text" class="form-control" id="nomor_po_supplier" name="nomor_po_supplier" 
                       placeholder="Nomor PO dari supplier (jika ada)">
                <span class="help-block"></span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="form-group">
                <label for="alamat_pengiriman" class="control-label">Alamat Pengiriman</label>
                <textarea class="form-control" id="alamat_pengiriman" name="alamat_pengiriman" rows="3" 
                          placeholder="Alamat lengkap untuk pengiriman barang"></textarea>
                <span class="help-block"></span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="form-group">
                <label for="keterangan" class="control-label">Keterangan</label>
                <textarea class="form-control" id="keterangan" name="keterangan" rows="3" 
                          placeholder="Keterangan tambahan untuk pembelian ini"></textarea>
                <span class="help-block"></span>
            </div>
        </div>
    </div>
</form>

<script>
$(document).ready(function() {
    $('.select2').select2({
        dropdownParent: $('#modal_form')
    });

    // Event handler untuk perubahan supplier
    $('#id_supplier').on('change', function() {
        var supplierId = $(this).val();
        if (supplierId) {
            $.ajax({
                url: '<?= site_url('pembelian/get_supplier_info') ?>',
                type: 'POST',
                data: { id: supplierId },
                dataType: 'json',
                success: function(response) {
                    if (response.status) {
                        // Auto fill alamat pengiriman dengan alamat supplier
                        if (!$('#alamat_pengiriman').val()) {
                            $('#alamat_pengiriman').val(response.data.alamat);
                        }
                    }
                }
            });
        }
    });

    // Set tanggal jatuh tempo otomatis (30 hari dari tanggal pembelian)
    $('#tanggal_pembelian').on('change', function() {
        var tanggalPembelian = new Date($(this).val());
        if (tanggalPembelian) {
            tanggalPembelian.setDate(tanggalPembelian.getDate() + 30);
            var tahun = tanggalPembelian.getFullYear();
            var bulan = String(tanggalPembelian.getMonth() + 1).padStart(2, '0');
            var hari = String(tanggalPembelian.getDate()).padStart(2, '0');
            $('#tanggal_jatuh_tempo').val(tahun + '-' + bulan + '-' + hari);
        }
    });
});
</script>