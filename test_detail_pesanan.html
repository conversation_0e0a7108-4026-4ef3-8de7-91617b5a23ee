<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Detail Pesanan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h2>Test Detail Pesanan dari Pengiriman</h2>
                <p>Halaman ini untuk menguji fungsi detail pesanan ketika nomor pesanan diklik di daftar pengiriman.</p>
                
                <div class="card">
                    <div class="card-header">
                        <h5>Test Functionality</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="id_pesanan" class="form-label">ID Pesanan:</label>
                            <input type="number" class="form-control" id="id_pesanan" value="1" placeholder="Masukkan ID pesanan">
                            <small class="text-muted">Gunakan ID pesanan yang sudah ada (contoh: 1, 2, 3)</small>
                        </div>
                        
                        <button type="button" class="btn btn-primary" onclick="testDetailPesanan()">
                            Test Detail Pesanan
                        </button>
                        
                        <button type="button" class="btn btn-info" onclick="simulateClick()">
                            Simulasi Klik Nomor Pesanan
                        </button>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>Simulasi Tabel Pengiriman</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Nomor Pengiriman</th>
                                    <th>Tanggal</th>
                                    <th>Pelanggan</th>
                                    <th>Nomor Pesanan</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>PGR-20241201-0001</td>
                                    <td>01/12/2024</td>
                                    <td>PT. Contoh<br><small class="text-muted">CUST001</small></td>
                                    <td>
                                        <a href="javascript:void(0)" onclick="detail_pesanan(1)" class="text-primary">
                                            PSN-20241201-0001
                                        </a>
                                    </td>
                                    <td><span class="badge bg-warning">Draft</span></td>
                                </tr>
                                <tr>
                                    <td>PGR-20241201-0002</td>
                                    <td>01/12/2024</td>
                                    <td>CV. Elektronik<br><small class="text-muted">CUST002</small></td>
                                    <td>
                                        <a href="javascript:void(0)" onclick="detail_pesanan(2)" class="text-primary">
                                            PSN-20241201-0002
                                        </a>
                                    </td>
                                    <td><span class="badge bg-info">Prepared</span></td>
                                </tr>
                                <tr>
                                    <td>PGR-20241201-0003</td>
                                    <td>01/12/2024</td>
                                    <td>Toko ABC<br><small class="text-muted">CUST003</small></td>
                                    <td>
                                        <a href="javascript:void(0)" onclick="detail_pesanan(3)" class="text-primary">
                                            PSN-20241201-0003
                                        </a>
                                    </td>
                                    <td><span class="badge bg-success">Shipped</span></td>
                                </tr>
                            </tbody>
                        </table>
                        <p class="text-muted">
                            <i class="fa fa-info-circle"></i> 
                            Klik pada nomor pesanan di kolom "Nomor Pesanan" untuk melihat detail pesanan.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Copy the exact function from pengiriman.php
        function detail_pesanan(id_pesanan) {
            // Show loading
            Swal.fire({
                title: 'Memuat Detail Pesanan...',
                text: 'Sedang mengambil data pesanan, mohon tunggu.',
                icon: 'info',
                allowOutsideClick: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Get pesanan detail via AJAX
            $.ajax({
                url: "index.php/pengiriman/get_pesanan_info",
                type: "POST",
                data: { 
                    id_pesanan: id_pesanan 
                },
                dataType: "JSON",
                success: function(response) {
                    Swal.close();
                    
                    if (response.status) {
                        show_pesanan_detail_modal(response);
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: response.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    Swal.close();
                    console.log('Error details:', {
                        status: jqXHR.status,
                        statusText: jqXHR.statusText,
                        responseText: jqXHR.responseText,
                        textStatus: textStatus,
                        errorThrown: errorThrown
                    });
                    
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat mengambil detail pesanan. Check console untuk detail.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }

        function show_pesanan_detail_modal(data) {
            const pesanan = data.pesanan;
            const detail = data.detail;
            const shipping_history = data.shipping_history;

            // Build detail items table
            let detailTable = '';
            if (detail && detail.length > 0) {
                detailTable = `
                    <table class="table table-sm table-bordered">
                        <thead class="bg-light">
                            <tr>
                                <th>No</th>
                                <th>Kode Barang</th>
                                <th>Nama Barang</th>
                                <th>Qty</th>
                                <th>Satuan</th>
                                <th>Harga Satuan</th>
                                <th>Subtotal</th>
                            </tr>
                        </thead>
                        <tbody>`;
                
                detail.forEach((item, index) => {
                    detailTable += `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${item.kode_barang}</td>
                            <td>${item.nama_barang}${item.merk ? '<br><small class="text-muted">' + item.merk + ' ' + (item.tipe || '') + '</small>' : ''}</td>
                            <td>${parseFloat(item.qty).toLocaleString()}</td>
                            <td>${item.nama_satuan || '-'}</td>
                            <td>Rp ${parseFloat(item.harga_satuan).toLocaleString()}</td>
                            <td>Rp ${parseFloat(item.subtotal).toLocaleString()}</td>
                        </tr>`;
                });
                
                detailTable += `</tbody></table>`;
            } else {
                detailTable = '<p class="text-muted">Tidak ada detail item.</p>';
            }

            // Build shipping history table
            let shippingTable = '';
            if (shipping_history && shipping_history.length > 0) {
                shippingTable = `
                    <table class="table table-sm table-bordered">
                        <thead class="bg-light">
                            <tr>
                                <th>Nomor Pengiriman</th>
                                <th>Tanggal</th>
                                <th>Status</th>
                                <th>Ekspedisi</th>
                                <th>Total Item</th>
                                <th>Total Qty</th>
                            </tr>
                        </thead>
                        <tbody>`;
                
                shipping_history.forEach((ship) => {
                    const statusBadge = getStatusBadge(ship.status);
                    shippingTable += `
                        <tr>
                            <td>${ship.nomor_pengiriman}</td>
                            <td>${formatDate(ship.tanggal_pengiriman)}</td>
                            <td>${statusBadge}</td>
                            <td>${ship.ekspedisi || '-'}</td>
                            <td>${ship.total_item}</td>
                            <td>${parseFloat(ship.total_qty).toLocaleString()}</td>
                        </tr>`;
                });
                
                shippingTable += `</tbody></table>`;
            } else {
                shippingTable = '<p class="text-muted">Belum ada riwayat pengiriman.</p>';
            }

            // Show modal with pesanan detail
            Swal.fire({
                title: `Detail Pesanan: ${pesanan.nomor_pesanan}`,
                html: `
                    <div class="text-left">
                        <!-- Pesanan Info -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>Tanggal Pesanan:</strong><br>
                                ${formatDate(pesanan.tanggal_pesanan)}
                            </div>
                            <div class="col-md-6">
                                <strong>Status:</strong><br>
                                ${getStatusBadge(pesanan.status)}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>Pelanggan:</strong><br>
                                ${pesanan.nama_pelanggan}<br>
                                <small class="text-muted">${pesanan.kode_pelanggan}</small>
                            </div>
                            <div class="col-md-6">
                                <strong>Jenis Pesanan:</strong><br>
                                ${pesanan.jenis_pesanan}
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <strong>Total Item:</strong><br>
                                ${pesanan.total_item}
                            </div>
                            <div class="col-md-4">
                                <strong>Total Qty:</strong><br>
                                ${parseFloat(pesanan.total_qty).toLocaleString()}
                            </div>
                            <div class="col-md-4">
                                <strong>Total Harga:</strong><br>
                                Rp ${parseFloat(pesanan.total_harga).toLocaleString()}
                            </div>
                        </div>

                        ${pesanan.keterangan ? `
                        <div class="row mb-3">
                            <div class="col-12">
                                <strong>Keterangan:</strong><br>
                                ${pesanan.keterangan}
                            </div>
                        </div>
                        ` : ''}

                        <!-- Detail Items -->
                        <hr>
                        <h6><i class="fa fa-list"></i> Detail Item Pesanan</h6>
                        ${detailTable}

                        <!-- Shipping History -->
                        <hr>
                        <h6><i class="fa fa-shipping-fast"></i> Riwayat Pengiriman</h6>
                        ${shippingTable}
                    </div>
                `,
                width: '90%',
                showConfirmButton: true,
                confirmButtonText: 'Tutup',
                confirmButtonColor: '#6c757d'
            });
        }

        function getStatusBadge(status) {
            const statusMap = {
                // Pesanan statuses - consistent with Pesanan.php controller
                'draft': { class: 'warning', text: 'Draft' },
                'diproses': { class: 'primary', text: 'Diproses' },
                'dikirim': { class: 'info', text: 'Dikirim' },
                'selesai': { class: 'success', text: 'Selesai' },
                'dibatalkan': { class: 'danger', text: 'Dibatalkan' },
                // Pengiriman statuses - consistent with existing modules
                'prepared': { class: 'info', text: 'Prepared' },
                'shipped': { class: 'primary', text: 'Shipped' },
                'in_transit': { class: 'warning', text: 'In Transit' },
                'delivered': { class: 'success', text: 'Delivered' },
                'returned': { class: 'danger', text: 'Returned' },
                'cancelled': { class: 'secondary', text: 'Cancelled' }
            };

            const statusInfo = statusMap[status] || { class: 'secondary', text: status };
            return `<span class="badge badge-${statusInfo.class}">${statusInfo.text}</span>`;
        }

        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('id-ID', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
            });
        }

        function testDetailPesanan() {
            const id_pesanan = $('#id_pesanan').val();
            
            if (!id_pesanan) {
                Swal.fire('Error', 'Masukkan ID pesanan terlebih dahulu', 'error');
                return;
            }
            
            detail_pesanan(id_pesanan);
        }

        function simulateClick() {
            // Simulate clicking on the first pesanan link
            detail_pesanan(1);
        }
    </script>
</body>
</html>
