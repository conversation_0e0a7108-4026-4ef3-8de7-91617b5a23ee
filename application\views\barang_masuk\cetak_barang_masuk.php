<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Cetak Barang Masuk</title>
    <link rel="stylesheet" href="<?= base_url('assets/dist/css/adminlte.min.css') ?>">
    <style>
        @media print {
            .no-print { display: none !important; }
            body { font-size: 12px; }
            .table { font-size: 11px; }
        }
        .header-company {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }
        .report-title {
            text-align: center;
            margin: 20px 0;
            font-weight: bold;
            font-size: 16px;
        }
        .table {
            border-collapse: collapse;
            width: 100%;
        }
        .table th, .table td {
            border: 1px solid #000;
            padding: 5px;
            text-align: left;
        }
        .table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .footer-info {
            margin-top: 30px;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="header-company">
            <h2>TOKO ELEKTRONIK</h2>
            <p>Barang Masuk</p>
        </div>
        <div class="report-title">
            Data Barang Masuk No: <?= $barang_masuk->nomor_penerimaan ?><br>
            Tanggal: <?= date('d/m/Y', strtotime($barang_masuk->tanggal)) ?><br>
            Jenis: <?= ucfirst(str_replace('_', ' ', $barang_masuk->jenis)) ?><br>
            <?php if ($barang_masuk->jenis == 'pembelian' && $barang_masuk->nama_supplier): ?>
                Supplier: <?= $barang_masuk->nama_supplier ?><br>
            <?php endif; ?>
            <?php if ($barang_masuk->jenis == 'transfer_masuk' && $barang_masuk->ref_nomor): ?>
                Ref Transfer: <?= $barang_masuk->ref_nomor ?><br>
            <?php endif; ?>
            Dibuat oleh: <?= $barang_masuk->nama_created_by ?? 'System' ?>
        </div>
        <table class="table">
            <thead>
                <tr>
                    <th>No</th>
                    <th>Kode Barang</th>
                    <th>Nama Barang</th>
                    <th>Gudang</th>
                    <th>Qty</th>
                    <th>Satuan</th>
                    <th>Keterangan</th>
                </tr>
            </thead>
            <tbody>
                <?php $no=1; foreach($detail_list as $row): ?>
                <tr>
                    <td><?= $no++ ?></td>
                    <td><?= $row->kode_barang ?></td>
                    <td><?= $row->nama_barang ?></td>
                    <td><?= $row->nama_gudang ?></td>
                    <td class="text-right"><?= number_format($row->qty_masuk,2) ?></td>
                    <td><?= $row->nama_satuan ?></td>
                    <td><?= $row->keterangan ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <div class="footer-info">
            <p>Dicetak pada: <?= date('d/m/Y H:i:s') ?></p>
        </div>
        <div class="no-print" style="text-align: center; margin-top: 30px;">
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print"></i> Cetak
            </button>
            <button onclick="window.close()" class="btn btn-secondary">
                <i class="fas fa-times"></i> Tutup
            </button>
        </div>
    </div>
</body>
</html>